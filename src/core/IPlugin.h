//
//  IPlugin.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef IPlugin_h
#define IPlugin_h

#include "common/common.h"
#include "common/msg.h"
#include "utils/utils.h"
#include "3rd/json/json.hpp"
#include "core/Queue.hpp"
#include "core/CWorker.hpp"  // 需要包含CWorker的完整定义
#include <vector>

// 前向声明
class ITasker;
class IPlugin;
#include <map>

using namespace std;
using json = nlohmann::json;

struct PluginInfo
{
    string _name;
    string _module;
    string _description;

    // 数据流消息ID
    vector<uint32_t> _dataflow_input;   // 输入数据流消息ID
    vector<uint32_t> _dataflow_output;  // 输出数据流消息ID

    // 控制流消息ID
    vector<uint32_t> _controlflow_receive;  // 接收的控制流消息ID

    uint32_t _workerNum;

    PluginInfo(string path)
    {
        std::ifstream f(path.c_str(), ios::in);
        json conf = json::parse(f);
        f.close();

        _name = conf["name"];
        _module = conf["module"];
        _description = conf["description"];
        _workerNum = conf["workerNum"];

        // 解析数据流配置
        if (conf.contains("dataflow")) {
            if (conf["dataflow"].contains("input")) {
                parseMessageIds(conf["dataflow"]["input"], _dataflow_input);
            }
            if (conf["dataflow"].contains("output")) {
                parseMessageIds(conf["dataflow"]["output"], _dataflow_output);
            }
        }

        // 解析控制流配置 - 现在直接是数组
        if (conf.contains("controlflow") && conf["controlflow"].is_array()) {
            for (int element : conf["controlflow"]) {
                _controlflow_receive.push_back(element);
            }
        }
    }

    ~PluginInfo()
    {
        printf("free PluginInfo \n");
    }

private:
    // 辅助函数：解析消息ID（支持数组、字符串、数字格式）
    void parseMessageIds(const json& value, vector<uint32_t>& target) {
        if (value.is_array()) {
            for (int element : value) {
                target.push_back(element);
            }
        } else if (value.is_string()) {
            string str = value;
            if (!str.empty()) {
                target.push_back(std::stoi(str));
            }
        } else if (value.is_number()) {
            target.push_back(value);
        }
    }
};



class IPlugin
{
public:
    IPlugin() : m_worker(nullptr) {
        // 在构造函数中创建队列和Worker
        m_queue = new Queue();
        m_downstream_queue = nullptr;  // 下游队列需要从其他插件注册

        // 创建Worker
        m_worker = new CWorker();
    }

    // 析构函数 - 管理Queue和Worker的生命周期（组合关系）
    virtual ~IPlugin() {
        // 删除Worker（组合关系）- Worker的析构函数会处理线程停止
        if (m_worker) {
            delete m_worker;
            m_worker = nullptr;
        }

        // 删除队列（组合关系）
        if (m_queue) {
            delete m_queue;
            m_queue = nullptr;
        }
        // m_downstream_queue 不删除，因为它是从其他插件获取的引用

        // 删除Tasker
        if (m_tasker) {
            delete m_tasker;
            m_tasker = nullptr;
        }

        printf("插件析构完成\n");
    }

    virtual PluginInfo* info() = 0;

    virtual int telemetry(void *msg) {
        // 默认实现：什么都不做，子类可以重写
        (void)msg;
        return 0;
    }

    // 已删除：processMessage 方法不再需要
    // Worker 会直接调用 Tasker->excute(msg) 处理消息
    // 已删除：handle 方法不再需要
    // Tasker 会直接将消息传递给下游的队列

    virtual void init() = 0;

    virtual void uninit() = 0;

    virtual void start() = 0;

    virtual void stop() = 0;

    // 获取队列接口（供上游Plugin获取下游Plugin的队列进行消息传递）
    Queue* getQueue() { return m_queue; }

    // 设置下游队列
    void setup(Queue* downstreamQueue) {
        m_downstream_queue = downstreamQueue;

        // 当下游队列设置完成后，配置Worker
        if (m_downstream_queue) {
            m_worker->setup(m_queue, m_downstream_queue);
        }

        if (m_tasker) {
            m_worker->setup(m_tasker);
        }
    }



    // 控制接口（用于直接同步调用，不通过队列）
    virtual void control(int msgID, void* controlData = nullptr) {
        // 子类可以重写此方法来处理具体的控制逻辑
    }



protected:
    // Queue 管理 - Plugin拥有Queue（组合关系）
    Queue* m_queue = nullptr;                            // 插件的处理队列（用来消费和处理）
    Queue* m_downstream_queue = nullptr;                 // 下游队列（用于向下游传递消息）

    // Worker关联 - Plugin通过Worker与队列交互
    CWorker* m_worker = nullptr;                         // 关联的Worker（负责队列管理）

    // 插件信息
    PluginInfo *m_info;

    // Tasker 管理 - 每个Plugin只有一个Tasker
    ITasker* m_tasker = nullptr;                         // 插件的业务处理器
};

#endif /* IPlugin_h */