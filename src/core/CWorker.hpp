//
//  CWorker.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef CWorker_h
#define CWorker_h

#include "common/common.h"
#include "core/Queue.hpp"
#include "core/ITasker.h"

using namespace std;

// Plugin中创建CWorker,创建Tasker,并且将Tasker注册到Worker中

class CWorker
{
public:
    CWorker():m_tasker(nullptr), m_queue(nullptr), m_downstream_queue(nullptr), m_thread_running(false)
    {
        // 简化：不在构造函数中创建线程，等待setupQueues和injectTasker后再启动
    }

    virtual ~CWorker()
    {
        LOG_INFO("delete CWorker");
        // 停止处理线程并清理资源
        stop();
        if (m_worker_thread.joinable()) {
            m_worker_thread.join();
            LOG_INFO("Worker 处理线程已停止");
        }
        m_tasker = nullptr;
    }

    // 删除旧的produce和clear方法 - 不再需要内部队列

    // 设置队列
    void setup(Queue* queue, Queue* downstreamQueue) {
        m_queue = queue;
        m_downstream_queue = downstreamQueue;
        LOG_INFO("Worker 队列设置完成: queue=" << queue << ", downstream=" << downstreamQueue);
    }

    // 设置 Tasker 并启动
    void setup(ITasker* tasker) {
        m_tasker = tasker;
        LOG_INFO("Worker Tasker 设置完成: " << tasker);

        // 如果队列和 Tasker 都设置好了，自动启动
        if (m_queue && m_tasker) {
            start();
        } else {
            LOG_WARN("Worker 设置不完整，无法启动");
        }
    }

    // 启动Worker - 创建线程并开始处理消息
    void start() {
        if (!m_queue || !m_tasker) {
            LOG_ERROR("Worker 缺少处理队列或Tasker");
            return;
        }

        if (m_worker_thread.joinable()) {
            LOG_WARN("Worker 线程已经在运行");
            return;
        }

        m_thread_running = true;
        m_worker_thread = std::thread(&CWorker::handle, this);
        LOG_INFO("Worker 处理线程创建成功并开始处理消息");
    }

    // 停止Worker - 停止处理消息
    void stop() {
        m_thread_running = false;
        LOG_INFO("Worker 停止处理消息");
    }

    // 处理消息的线程函数
    void handle() {
        LOG_INFO("Worker 处理线程开始运行");

        while (m_thread_running) {
            void* msg = nullptr;
            int result = m_queue->comsume(&msg);

            if (result == QUEUE_OK && msg != nullptr) {
                // printf("Worker: 从队列获取到消息 %p\n", msg);
                try {
                    // 使用Tasker处理消息，获取处理结果
                    void* processedMsg = m_tasker->execute(msg);

                    // 如果有处理结果且有下游队列，则推送到下游
                    if (processedMsg && m_downstream_queue) {
                        m_downstream_queue->produce(processedMsg);
                    }
                } catch (...) {
                    LOG_ERROR("Worker 处理线程处理消息时发生异常");
                }
            } else {
                // 没有消息，短暂休眠
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }

        LOG_INFO("Worker 处理线程结束运行");
    }

private:
    ITasker* m_tasker;

    Queue* m_queue;              // 处理队列（用来消费和处理）
    Queue* m_downstream_queue;   // 下游队列（用于向下游传递消息）

    // 线程管理 - Worker拥有处理线程
    std::thread m_worker_thread; // 工作线程
    bool m_thread_running;       // 线程运行状态
};


#endif /* CWorker_h */
