//
//  CUploadTasker.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef C_Upload_Tasker_h
#define C_Upload_Tasker_h

#include "common/common.h"
#include "common/msg.h"
#include "utils/utils.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"

using namespace std;

class CUploadTasker : public ITasker
{
public:
    CUploadTasker(IPlugin *plugin);
    virtual ~CUploadTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;
private:
    [[maybe_unused]] IPlugin *m_plugin;
};

#endif /* C_Upload_Tasker_h */