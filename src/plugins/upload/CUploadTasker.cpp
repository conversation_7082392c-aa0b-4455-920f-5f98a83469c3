//
//  CUploadTasker.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CUploadTasker.h"

CUploadTasker::CUploadTasker(IPlugin *plugin): m_plugin(plugin)
{
    // printf("new CUploadTasker \n");
    (void)m_plugin;  // 避免未使用变量警告
}

CUploadTasker::~CUploadTasker()
{
    // printf("delete CUploadTasker  \n");
}

void* CUploadTasker::execute(void *msg)
{
    if(!msg)
    {
        usleep(1000000);
        return nullptr;
    }

    // 尝试识别消息类型
    // 首先检查是否为HTTP解析结果（msgID 22）
    HTTPParseResult* httpResult = (HTTPParseResult*)msg;
    bool isHTTPParseResult = false;

    try {
        // 更安全的类型检查：检查指针有效性和isValid字段
        if (httpResult &&
            (uintptr_t)httpResult > 0x1000 &&  // 基本指针有效性检查
            httpResult->isValid == true) {
            isHTTPParseResult = true;
        }
    } catch (...) {
        isHTTPParseResult = false;
    }

    if (isHTTPParseResult) {
        printf("CUploadHandler: 接收到HTTP解析结果开始上传\n");
        printf("  - 请求: %s %s\n", httpResult->method.c_str(), httpResult->url.c_str());
        printf("  - 连接: %s:%d -> %s:%d\n",
               httpResult->srcIP.c_str(), httpResult->srcPort,
               httpResult->dstIP.c_str(), httpResult->dstPort);
        printf("  - 时间戳: %d\n", httpResult->timestamp);

        // 模拟HTTP解析结果上传处理
        usleep(50000);  // 50ms上传时间
        printf("CUploadHandler: HTTP解析结果上传完成\n");

        // 这里可以添加实际的上传逻辑，比如：
        // - 发送到远程服务器
        // - 保存到文件
        // - 发送到数据库等

        delete httpResult;
        return nullptr;  // Upload是最后环节，不需要返回消息
    }

    // 检查是否为解析后的网络数据包消息
    ParserToUploadData *parsedData = (ParserToUploadData*)msg;

    // 添加安全检查，确保指针有效且格式正确
    bool isParsedNetworkPacket = false;
    try {
        if (parsedData && parsedData->dataSize > 0 && parsedData->dataSize < 65536 &&
            parsedData->format == "parsed_network_packet" && parsedData->isValid) {
            isParsedNetworkPacket = true;
        }
    } catch (...) {
        // 如果访问失败，说明不是有效的ParserToUploadData
        isParsedNetworkPacket = false;
    }

    if (isParsedNetworkPacket) {
        // printf("CUploadHandler: 接收到解析后的网络数据包 (大小: %zu bytes, 格式: %s) 开始上传\n",
        //        parsedData->dataSize, parsedData->format.c_str());

        // 模拟网络数据包上传处理
        usleep(50000);  // 50ms上传时间

        // printf("CUploadHandler: 网络数据包上传完成 - 大小: %zu bytes, 时间戳: %d\n",
        //        parsedData->dataSize, parsedData->timestamp);

        // 这里可以添加实际的上传逻辑，比如：
        // - 发送到远程服务器
        // - 保存到文件
        // - 发送到数据库等

        delete parsedData;
        return nullptr;  // Upload是最后环节，不需要返回消息
    }

    // 处理传统的MyMsg消息（向后兼容）
    MyMsg *m = (MyMsg*)msg;
    printf("CUploadHandler: 接收到数据流消息 (数据值: %d) 开始上传处理\n", m->m_i);
    sleep(2);  // 模拟上传处理时间
    printf("CUploadHandler: 数据上传完成 - 数据值: %d\n", m->m_i);

    delete m;
    return nullptr;  // Upload是最后环节，不需要返回消息
}

void CUploadTasker::clear()
{
    return;
}