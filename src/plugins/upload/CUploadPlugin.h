//
//  DemoUpload.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef DemoUpload_hpp
#define DemoUpload_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CUploadTasker.h"
#include <memory>

class CUploadPlugin : public IPlugin
{
public:
    CUploadPlugin();
    virtual ~CUploadPlugin();
    virtual PluginInfo* info() override;
    // handle方法已移除 - Tasker会直接将消息传递给下游的队列
    // control 和 telemetry 现在有默认实现，不需要重写
    virtual void init() override;
    virtual void uninit() override {}
    virtual void start() override;
    virtual void stop() override {}

    // 已删除：processMessage 方法不再需要
    void simulate_data_uploading();
    static void* simulate_data_uploading_wrapper(void* arg);
    
private:
    pthread_t m_thread_id;
};


#endif /* DemoUpload_hpp */