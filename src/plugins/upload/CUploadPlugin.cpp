//
//  DemoUpload.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CUploadPlugin.h"

CUploadPlugin::CUploadPlugin()
{
    m_info = new PluginInfo("./config/upload.json");
}

CUploadPlugin::~CUploadPlugin()
{
    // printf("delete CPlugin \n");
    delete m_info;
    pthread_cancel(m_thread_id);
}

PluginInfo* CUploadPlugin::info()
{
    return m_info;
}

// handle方法已移除 - Tasker会直接将消息传递给下游的队列

void CUploadPlugin::init()
{
    // 创建 Tasker（在 initDI() 之前创建，这样 setup() 时会自动注入）
    m_tasker = new CUploadTasker(this);
}

void CUploadPlugin::start()
{
    // Tasker 已在 init() 中创建，并在 initDI() 中自动注入到 Worker

    // 启动模拟数据接收线程 - 使用 pthread
    pthread_create(&m_thread_id, NULL, simulate_data_uploading_wrapper, this);
}

// pthread 包装函数
void* CUploadPlugin::simulate_data_uploading_wrapper(void* arg)
{
    CUploadPlugin* upload = (CUploadPlugin*)arg;
    upload->simulate_data_uploading();
    return NULL;
}

void CUploadPlugin::simulate_data_uploading()
{
    for(;;)
    {
        // 模拟接收来自 parser 的数据流消息（ID 21）
        static int data_counter = 300;
        MyMsg *msg = new MyMsg{data_counter++, 2, nullptr};
        // printf("DemoUpload: 模拟接收来自 parser 的数据 (值: %d)\n", msg->m_i);
        // 直接将消息放入自己的队列
        if (m_queue) {
            m_queue->produce(msg);
        } else {
            delete msg;
        }
        sleep(8);  // 每8秒处理一次数据
    }
}

// 已删除：processMessage 方法不再需要
// Worker 会直接调用 Tasker->excute(msg) 处理消息