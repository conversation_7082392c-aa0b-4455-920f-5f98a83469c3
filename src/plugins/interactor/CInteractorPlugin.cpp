//
//  CInteractorPlugin.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CInteractorPlugin.h"
#include "common/MessageFactory.h"
#include <unistd.h>  // for pipe, close

CInteractorPlugin::CInteractorPlugin()
{
    m_info = new PluginInfo("./config/interactor.json");
    m_dispatcher = nullptr;
    m_running = false;

    // 创建管道用于接收 web 输入
    if (pipe(m_web_pipe) == -1) {
        printf("错误: 创建 web 输入管道失败\n");
        m_web_pipe[0] = m_web_pipe[1] = -1;
    } else {
        printf("CInteractorPlugin: Web 输入管道创建成功 [读端:%d, 写端:%d]\n",
               m_web_pipe[0], m_web_pipe[1]);
    }
}

CInteractorPlugin::~CInteractorPlugin()
{
    printf("free DemoInteractor \n");
    stop();

    delete m_info;

    sleep(1);  // 使用 C 风格的 sleep
    pthread_cancel(m_thread_id);

    // 关闭管道
    if (m_web_pipe[0] != -1) close(m_web_pipe[0]);
    if (m_web_pipe[1] != -1) close(m_web_pipe[1]);
}

PluginInfo* CInteractorPlugin::info()
{
    return m_info;
}

// handle方法已移除 - Tasker会直接将消息传递给下游的队列

void CInteractorPlugin::init()
{
    printf("CInteractorPlugin::init() - 初始化交互插件\n");
    m_running = false;
}

void CInteractorPlugin::uninit()
{
    printf("CInteractorPlugin::uninit() - 清理交互插件\n");
    stop();
}

void CInteractorPlugin::start()
{
    // printf("CInteractorPlugin::start() - 启动交互插件\n");
    m_running = true;

    // 调用父类的 start() 方法，启动 worker（即使这个插件可能不使用worker处理消息）
    IPlugin::start();

    // Interactor 插件不通过工作线程消费消息，其交互逻辑在单独线程中进行
    // 因此，不创建 handlers 和启动工作线程
    // m_workers_size = 3;
    // for(int i = 0; i < m_workers_size; i++)
    // {
    //     IHandler *handler = new CInteractorHandler(this);
    //     m_handlers.push_back(handler);
    // }
    // start_worker_threads(m_workers_size);

    // 使用 pthread 替代 std::thread
    pthread_create(&m_thread_id, NULL, simulate_interaction_wrapper, this);

    // 启动 Web 输入监听线程
    start_web_input_listener();

    return;
}

void CInteractorPlugin::stop()
{
    printf("CInteractorPlugin::stop() - 停止交互插件\n");
    m_running = false;

    // 调用父类的 stop() 方法，停止 worker
    IPlugin::stop();

    // 这里不需要取消线程，因为使用的是 std::thread
    // 线程会在 simulate_interaction 检查 m_running 后自然退出
}

// pthread 包装函数
void* CInteractorPlugin::simulate_interaction_wrapper(void* arg)
{
    CInteractorPlugin* interactor = (CInteractorPlugin*)arg;
    interactor->simulate_interaction();
    return NULL;
}

void CInteractorPlugin::simulate_interaction()
{
    while(m_running)
    {
        static int n = 0;
        // 使用消息工厂创建控制消息，自动填充msgID
        SourceInteractionMsg* mo = MessageFactory::createSourceControlMsg("simulate", "test_command", to_string(n++));

        // 通过分发器插件发送消息
        if (m_dispatcher) {
            // 直接将消息放入dispatcher的队列
            Queue* dispatcherQueue = m_dispatcher->getQueue();
            if (dispatcherQueue) {
                dispatcherQueue->produce(mo);
            } else {
                printf("警告: DemoInteractor dispatcher队列未初始化\n");
                delete mo;
            }
        } else {
            printf("警告: DemoInteractor 没有设置分发器插件\n");
            delete mo;
        }

        sleep(6);
    }
    printf("CInteractorPlugin::simulate_interaction() - 线程退出\n");
}

// 启动 Web 输入监听线程
void CInteractorPlugin::start_web_input_listener()
{
    if (m_web_pipe[0] == -1) {
        printf("错误: Web 输入管道未创建，无法启动监听线程\n");
        return;
    }

    pthread_create(&m_web_listener_thread, NULL, web_input_listener_thread, this);
    printf("CInteractorPlugin: Web 输入监听线程已启动\n");
}

// Web 输入监听线程函数
void* CInteractorPlugin::web_input_listener_thread(void* arg)
{
    CInteractorPlugin* interactor = (CInteractorPlugin*)arg;

    printf("CInteractorPlugin: Web 输入监听线程开始运行\n");

    while (interactor->m_running) {
        // 模拟从管道读取 Web 输入
        // 实际项目中这里会从真实的 Web 接口或管道读取数据
        sleep(10);  // 每10秒模拟一次 Web 输入

        if (!interactor->m_running) break;

        // 模拟 Web 输入命令
        WebInputMsg* webMsg = new WebInputMsg{"start", "source", "", (int)time(nullptr)};
        interactor->process_web_input(webMsg);
        delete webMsg;

        sleep(5);
        if (!interactor->m_running) break;

        webMsg = new WebInputMsg{"stop", "parser", "", (int)time(nullptr)};
        interactor->process_web_input(webMsg);
        delete webMsg;
    }

    printf("CInteractorPlugin: Web 输入监听线程退出\n");
    return nullptr;
}

// 处理 Web 输入
void CInteractorPlugin::process_web_input(const WebInputMsg* webMsg)
{
    printf("CInteractorPlugin: 收到 Web 输入 - 命令:%s, 目标:%s, 参数:%s\n",
           webMsg->command.c_str(), webMsg->target.c_str(), webMsg->params.c_str());

    if (!m_dispatcher) {
        printf("错误: 分发器未设置，无法处理 Web 输入\n");
        return;
    }

    // 创建控制消息并通过分发器发送
    // 这里需要根据目标插件创建相应的控制消息
    if (webMsg->target == "source") {
        // 使用消息工厂创建源插件控制消息
        SourceInteractionMsg* controlMsg = MessageFactory::createSourceControlMsg(webMsg->command, webMsg->command, webMsg->params);
        // 直接将消息放入dispatcher的队列
        Queue* dispatcherQueue = m_dispatcher->getQueue();
        if (dispatcherQueue) {
            dispatcherQueue->produce(controlMsg);
        } else {
            delete controlMsg;
        }
        printf("CInteractorPlugin: 已发送控制消息到 source 插件\n");
    } else if (webMsg->target == "parser") {
        // 使用消息工厂创建解析器插件控制消息
        BaseControlMsg* controlMsg = MessageFactory::createParserControlMsg(webMsg->command);
        // 直接将消息放入dispatcher的队列
        Queue* dispatcherQueue = m_dispatcher->getQueue();
        if (dispatcherQueue) {
            dispatcherQueue->produce(controlMsg);
        } else {
            delete controlMsg;
        }
        printf("CInteractorPlugin: 已发送控制消息到 parser 插件\n");
    } else if (webMsg->target == "upload") {
        // 使用消息工厂创建上传器插件控制消息
        BaseControlMsg* controlMsg = MessageFactory::createUploadControlMsg(webMsg->command);
        // 直接将消息放入dispatcher的队列
        Queue* dispatcherQueue = m_dispatcher->getQueue();
        if (dispatcherQueue) {
            dispatcherQueue->produce(controlMsg);
        } else {
            delete controlMsg;
        }
        printf("CInteractorPlugin: 已发送控制消息到 upload 插件\n");
    } else {
        printf("警告: 未知的目标插件 %s\n", webMsg->target.c_str());
    }
}