//
//  CInteractorHandler.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CInteractorTasker.h"

CInteractorTasker::CInteractorTasker(IPlugin *plugin): m_plugin(plugin)
{
    printf("new CInteractorTasker \n");
    (void)m_plugin;  // 避免未使用变量警告
}

CInteractorTasker::~CInteractorTasker()
{
    printf("delete CInteractorTasker  \n");
}

void* CInteractorTasker::execute(void *msg)
{
    (void)msg;  // 避免未使用参数警告
    usleep(1000000);
    return nullptr;
}

void CInteractorTasker::clear()
{
    return;
}