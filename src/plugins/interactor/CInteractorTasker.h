//
//  CHandler.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef C_Interactor_Tasker_h
#define C_Interactor_Tasker_h

#include "common/common.h"
#include "common/msg.h"
#include "common/interactorMsg.h"
#include "utils/utils.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"

using namespace std;

class CInteractorTasker : public ITasker
{
public:
    CInteractorTasker(IPlugin *plugin);
    virtual ~CInteractorTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;
private:
    [[maybe_unused]] IPlugin *m_plugin;
};

#endif /* C_Interactor_Tasker_h */