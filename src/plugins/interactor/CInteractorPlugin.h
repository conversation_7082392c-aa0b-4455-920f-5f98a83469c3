//
//  CInteractorPlugin.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef CInteractorPlugin_hpp
#define CInteractorPlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CInteractorTasker.h"
#include <memory>

// 使用 common/interactorMsg.h 中定义的 WebInputMsg

class CInteractorPlugin : public IPlugin
{
public:
    CInteractorPlugin();
    virtual ~CInteractorPlugin();
    virtual PluginInfo* info() override;
    // handle方法已移除 - Tasker会直接将消息传递给下游的队列
    // control 和 telemetry 现在有默认实现，不需要重写
    virtual void init() override;
    virtual void uninit() override;
    virtual void start() override;
    virtual void stop() override;
    void simulate_interaction();

    // 设置分发器插件（只有InteractPlugin需要）
    void set_dispatcher(IPlugin* dispatcher) { m_dispatcher = dispatcher; }

    // Web 输入处理
    void start_web_input_listener();
    void process_web_input(const WebInputMsg* webMsg);

private:
    pthread_t m_thread_id;
    pthread_t m_web_listener_thread;  // Web 输入监听线程
    IPlugin* m_dispatcher;            // 分发器插件引用

    bool m_running;                   // 运行状态标志
    int m_web_pipe[2];               // Web 输入管道 [读端, 写端]

    // Web 输入监听线程函数
    static void* web_input_listener_thread(void* arg);
    static void* simulate_interaction_wrapper(void* arg);
};

#endif /* DemoInteractor_hpp */