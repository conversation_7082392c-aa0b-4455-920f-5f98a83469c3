# 插件构建配置

# 通用插件依赖
plugin_deps = [thread_dep, dl_dep, pcap_dep]
plugin_inc = include_directories('..', '../..')

# 插件管理器
plugin_manager_sources = [
  'framework/PluginManager.cpp',
  'framework/ModulePluginManager.cpp',
  # CWorker 现在是头文件实现，不需要单独编译
  # MessageManager 功能已集成到 PluginManager 中
]

plugin_manager = shared_library('plugin_manager',
  plugin_manager_sources,
  include_directories : plugin_inc,
  dependencies : plugin_deps,
  install : false
)

# 调度器插件
dispatcher_sources = [
  'dispatcher/CDispatcherPlugin.cpp',
  'dispatcher/CDispatcherTasker.cpp',
  'dispatcher/ModuleDispatcher.cpp'
]

plugin_demo_dispatcher = shared_library('plugin_demo_dispatcher',
  dispatcher_sources,
  include_directories : plugin_inc,
  dependencies : plugin_deps,
  install : false
)

# 交互器插件
interactor_sources = [
  'interactor/CInteractorPlugin.cpp',
  'interactor/CInteractorTasker.cpp',
  'interactor/ModuleInteractor.cpp'
]

plugin_demo_interactor = shared_library('plugin_demo_interactor',
  interactor_sources,
  include_directories : plugin_inc,
  dependencies : plugin_deps,
  install : false
)

# 监控器插件
monitor_sources = [
  'monitor/CMonitorPlugin.cpp',
  'monitor/CMonitorTasker.cpp',
  'monitor/ModuleMonitor.cpp'
]

plugin_demo_monitor = shared_library('plugin_demo_monitor',
  monitor_sources,
  include_directories : plugin_inc,
  dependencies : plugin_deps,
  install : false
)

# 解析器插件
parser_sources = [
  'parsers/CParserPlugin.cpp',
  'parsers/CParserTasker.cpp',
  'parsers/TCPSessionManager.cpp',
  'parsers/HTTPProtocolParser.cpp',
  'parsers/ModuleParser.cpp'
]

plugin_demo_parser = shared_library('plugin_demo_parser',
  parser_sources,
  include_directories : plugin_inc,
  dependencies : plugin_deps,
  install : false
)

# 数据源插件
source_sources = [
  'source/CSourcePlugin.cpp',
  'source/CSourceTasker.cpp',
  'source/PacketCapture.cpp',
  'source/ModuleSource.cpp'
]

plugin_demo_source = shared_library('plugin_demo_source',
  source_sources,
  include_directories : plugin_inc,
  dependencies : plugin_deps,
  install : false
)

# 上传插件
upload_sources = [
  'upload/CUploadPlugin.cpp',
  'upload/CUploadTasker.cpp',
  'upload/ModuleUpload.cpp'
]

plugin_demo_upload = shared_library('plugin_demo_upload',
  upload_sources,
  include_directories : plugin_inc,
  dependencies : plugin_deps,
  install : false
)

# 导出插件目标供主构建文件使用
all_plugins = [
  plugin_manager,
  plugin_demo_dispatcher,
  plugin_demo_interactor,
  plugin_demo_monitor,
  plugin_demo_parser,
  plugin_demo_source,
  plugin_demo_upload
]
