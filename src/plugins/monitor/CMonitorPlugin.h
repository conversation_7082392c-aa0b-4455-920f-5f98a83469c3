//
//  CMonitorPlugin.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef CMonitorPlugin_hpp
#define CMonitorPlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CMonitorTasker.h"
#include <memory>

// 使用 common/interactorMsg.h 中定义的 TelemetryData

class CMonitorPlugin : public IPlugin
{
public:
    CMonitorPlugin();
    virtual ~CMonitorPlugin();
    virtual PluginInfo* info() override;
    // handle方法已移除 - Tasker会直接将消息传递给下游的队列
    // control 现在有默认实现，不需要重写
    virtual int telemetry(void *msg) override;
    virtual void init() override;
    virtual void uninit() override;
    virtual void start() override;
    virtual void stop() override;

    // 设置其他插件的引用（用于调用 telemetry）
    void set_plugins(const map<string, IPlugin*>& plugins) { m_plugins = plugins; }

    // 设置分发器插件
    void set_dispatcher(IPlugin* dispatcher) { m_dispatcher = dispatcher; }

    // 定时任务相关
    void start_telemetry_collector();
    void collect_telemetry_data();

private:
    bool m_running;
    pthread_t m_telemetry_thread;           // 遥测收集线程
    map<string, IPlugin*> m_plugins;        // 其他插件的引用
    map<string, TelemetryData> m_telemetry_data;  // 遥测数据缓存
    IPlugin* m_dispatcher;                  // 分发器插件引用

    // 遥测收集线程函数
    static void* telemetry_collector_thread(void* arg);
};

#endif /* DemoMonitor_hpp */