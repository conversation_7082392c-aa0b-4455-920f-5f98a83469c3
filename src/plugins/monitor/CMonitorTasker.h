//
//  CHandler.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef C_Monitor_Tasker_h
#define C_Monitor_Tasker_h

#include "common/common.h"
#include "common/msg.h"
#include "common/interactorMsg.h"
#include "utils/utils.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"

using namespace std;

class CMonitorTasker : public ITasker
{
public:
    CMonitorTasker(IPlugin *plugin);
    virtual ~CMonitorTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;
private:
    [[maybe_unused]] IPlugin *m_plugin;
};

#endif /* C_Monitor_Tasker_h */