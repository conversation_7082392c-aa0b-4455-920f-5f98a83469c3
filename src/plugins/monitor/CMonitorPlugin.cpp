//
//  Monitor.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CMonitorPlugin.h"
#include "common/MessageFactory.h"
#include <ctime>     // for time, ctime
#include <cstdlib>   // for rand

CMonitorPlugin::CMonitorPlugin()
{
    m_info = new PluginInfo("./config/monitor.json");
    m_running = false;
    m_telemetry_thread = 0;
    m_dispatcher = nullptr;
}

CMonitorPlugin::~CMonitorPlugin()
{
    printf("free DemoMonitor \n");
    stop();
    delete m_info;
}

PluginInfo* CMonitorPlugin::info()
{
    return m_info;
}

// handle方法已移除 - Tasker会直接将消息传递给下游的队列

int CMonitorPlugin::telemetry(void *msg)
{
    printf("CMonitorPlugin::telemetry() - 返回监控插件遥测数据\n");

    // 返回当前监控插件的状态信息
    TelemetryData* data = (TelemetryData*)msg;
    if (data) {
        data->pluginName = "monitor";
        data->status = m_running ? "running" : "stopped";
        data->messageCount = rand() % 1000;  // 模拟消息计数
        data->cpuUsage = (rand() % 100) / 10.0;  // 模拟CPU使用率
        data->memoryUsage = rand() % 1024;  // 模拟内存使用量(MB)

        // 获取当前时间戳
        time_t now = time(0);
        data->timestamp = (int)now;
    }

    return 0;
}

void CMonitorPlugin::init()
{
    printf("CMonitorPlugin::init() - 初始化监控插件\n");
    m_running = false;
}

void CMonitorPlugin::uninit()
{
    printf("CMonitorPlugin::uninit() - 清理监控插件\n");
    stop();
}

void CMonitorPlugin::start()
{
    // printf("CMonitorPlugin::start() - 启动监控插件\n");
    m_running = true;

    // 调用父类的 start() 方法，启动 worker
    IPlugin::start();

    // Monitor 插件不通过工作线程消费消息，其遥测数据收集在单独线程中进行
    // 因此，不创建 handlers 和启动工作线程
    // m_workers_size = 3;
    // for(int i = 0; i < m_workers_size; i++)
    // {
    //     IHandler *handler = new CMonitorHandler(this);
    //     m_handlers.push_back(handler);
    // }
    // start_worker_threads(m_workers_size);

    // 启动遥测数据收集线程
    start_telemetry_collector();

    return;
}

void CMonitorPlugin::stop()
{
    printf("CMonitorPlugin::stop() - 停止监控插件\n");
    m_running = false;

    // 等待遥测收集线程结束
    if (m_telemetry_thread) {
        pthread_cancel(m_telemetry_thread);
        m_telemetry_thread = 0;
    }

    // 调用父类的 stop() 方法，停止 worker
    IPlugin::stop();
}

// 启动遥测数据收集线程
void CMonitorPlugin::start_telemetry_collector()
{
    pthread_create(&m_telemetry_thread, NULL, telemetry_collector_thread, this);
    printf("Monitor: 遥测数据收集线程已启动\n");
}

// 遥测收集线程函数
void* CMonitorPlugin::telemetry_collector_thread(void* arg)
{
    CMonitorPlugin* monitor = (CMonitorPlugin*)arg;

    printf("Monitor: 遥测收集线程开始运行\n");

    while (monitor->m_running) {
        monitor->collect_telemetry_data();
        sleep(15);  // 每15秒收集一次遥测数据
    }

    printf("Monitor: 遥测收集线程退出\n");
    return nullptr;
}

// 收集遥测数据
void CMonitorPlugin::collect_telemetry_data()
{
    printf("Monitor: 开始收集遥测数据...\n");

    // 遍历所有插件，调用它们的 telemetry 接口
    for (auto& pair : m_plugins) {
        const string& plugin_name = pair.first;
        IPlugin* plugin = pair.second;

        if (plugin && plugin_name != "monitor") {  // 不收集自己的数据
            // 使用消息工厂创建遥测消息，自动填充msgID
            TelemetryData* data = MessageFactory::createTelemetryMsg(plugin_name, "",
                                                                    rand() % 1000,
                                                                    (rand() % 100) / 10.0,
                                                                    rand() % 1000);

            // 调用插件的 telemetry 接口
            int result = plugin->telemetry(data);

            if (result == 0) {
                // 更新缓存的遥测数据
                m_telemetry_data.insert_or_assign(plugin_name, *data);

                printf("Monitor: 收集到 %s 插件遥测数据 - 状态:%s, 消息数:%d, CPU:%.1f%%, 内存:%ldMB\n",
                       plugin_name.c_str(), data->status.c_str(), data->messageCount,
                       data->cpuUsage, data->memoryUsage);

                // 释放工厂创建的消息
                delete data;
            } else {
                printf("Monitor: 从 %s 插件收集遥测数据失败\n", plugin_name.c_str());
            }
        }
    }

    printf("Monitor: 遥测数据收集完成\n");
}