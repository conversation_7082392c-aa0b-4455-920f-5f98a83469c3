//
//  CMonitorHandler.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CMonitorTasker.h"
#include "common/interactorMsg.h"

CMonitorTasker::CMonitorTasker(IPlugin *plugin): m_plugin(plugin)
{
    printf("new CMonitorTasker \n");
    (void)m_plugin;  // 避免未使用变量警告
}

CMonitorTasker::~CMonitorTasker()
{
    printf("delete CMonitorTasker  \n");
}

void* CMonitorTasker::execute(void *msg)
{
    if(!msg)
    {
        usleep(1000000);
        return nullptr;
    }
    MonitorMsg *m= (MonitorMsg*)msg;
    sleep(2);
    
    switch(m->msgID)
    {
        case 201:
        {
            SourceMonitorMsg *src_mo_msg = (SourceMonitorMsg*)m;
            printf("control msg monitor 201: messageCount=%d\n", src_mo_msg->messageCount);
            delete src_mo_msg;
            break;
        }
        default:
        {
            delete m;
            printf("control msg monitor other\n");
        }
    }
    return nullptr;
}

void CMonitorTasker::clear()
{
    return;
}