//
//  PacketCapture.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "PacketCapture.h"
#include <cstring>
#include <sstream>
#include <iomanip>

PacketCapture::PacketCapture()
    : m_handle(nullptr), m_isCapturing(false)
{
    memset(&m_stats, 0, sizeof(m_stats));
}

PacketCapture::~PacketCapture()
{
    stopCapture();
}

bool PacketCapture::initDevice(const string& deviceName, const string& filter)
{
    char errbuf[PCAP_ERRBUF_SIZE];
    
    // 如果没有指定设备名，自动选择默认设备
    if (deviceName.empty()) {
        vector<string> devices = getAvailableDevices();
        if (devices.empty()) {
            m_lastError = "无法找到可用的网卡设备";
            return false;
        }
        m_deviceName = devices[0];  // 使用第一个可用设备
    } else {
        m_deviceName = deviceName;
    }
    
    // 打开网卡设备
    printf("PacketCapture: 尝试打开网卡设备 %s\n", m_deviceName.c_str());
    m_handle = pcap_open_live(m_deviceName.c_str(), BUFSIZ, 1, 1000, errbuf);
    if (m_handle == nullptr) {
        m_lastError = "无法打开网卡设备 " + m_deviceName + ": " + string(errbuf);
        printf("PacketCapture: 错误 - %s\n", m_lastError.c_str());
        printf("PacketCapture: 提示 - 在 macOS 上可能需要管理员权限 (sudo)\n");
        return false;
    }

    printf("PacketCapture: 成功打开网卡设备 %s\n", m_deviceName.c_str());
    
    // 设置过滤器
    if (!filter.empty()) {
        if (!setFilter(filter)) {
            pcap_close(m_handle);
            m_handle = nullptr;
            return false;
        }
    }
    
    return true;
}

bool PacketCapture::startCapture()
{
    if (m_handle == nullptr) {
        m_lastError = "设备未初始化";
        return false;
    }
    
    m_isCapturing = true;
    // printf("PacketCapture: 开始抓包...\n");
    return true;
}

void PacketCapture::stopCapture()
{
    m_isCapturing = false;
    if (m_handle) {
        pcap_close(m_handle);
        m_handle = nullptr;
        // printf("PacketCapture: 停止抓包\n");
    }
}

NetworkPacketData* PacketCapture::getNextPacket()
{
    if (!m_isCapturing || m_handle == nullptr) {
        return nullptr;
    }

    struct pcap_pkthdr* header;
    const u_char* packet;

    // 捕获下一个数据包
    int result = pcap_next_ex(m_handle, &header, &packet);
    if (result != 1) {
        if (result == 0) {
            // 超时，没有数据包
            return nullptr;
        } else if (result == -1) {
            m_lastError = "抓包错误: " + string(pcap_geterr(m_handle));
            printf("PacketCapture: 抓包错误 - %s\n", m_lastError.c_str());
            return nullptr;
        } else if (result == -2) {
            // 文件结束（对于离线抓包）
            return nullptr;
        }
    }

    // 添加调试信息
    static int packetCount = 0;
    packetCount++;
    if (packetCount % 100 == 1) {  // 每100个包打印一次
        printf("PacketCapture: 已捕获 %d 个数据包\n", packetCount);
    }
    
    // 创建网络数据包消息
    NetworkPacketData* packetData = new NetworkPacketData();
    
    // 复制数据包内容
    packetData->packetSize = header->caplen;
    packetData->packetData = malloc(header->caplen);
    memcpy(packetData->packetData, packet, header->caplen);
    packetData->timestamp = header->ts.tv_sec;
    packetData->interface = m_deviceName;
    
    // 解析数据包头信息
    if (!parseEthernetHeader(packet, packetData)) {
        delete packetData;
        return nullptr;
    }
    
    m_stats.packetsReceived++;
    m_stats.packetsTotal++;
    
    return packetData;
}

vector<string> PacketCapture::getAvailableDevices()
{
    vector<string> devices;
    char errbuf[PCAP_ERRBUF_SIZE];
    pcap_if_t* alldevs;
    
    if (pcap_findalldevs(&alldevs, errbuf) == -1) {
        m_lastError = "获取网卡设备列表失败: " + string(errbuf);
        return devices;
    }
    
    for (pcap_if_t* dev = alldevs; dev != nullptr; dev = dev->next) {
        devices.push_back(dev->name);
    }
    
    pcap_freealldevs(alldevs);
    return devices;
}

bool PacketCapture::setFilter(const string& filterExpression)
{
    if (m_handle == nullptr) {
        m_lastError = "设备未初始化";
        return false;
    }
    
    struct bpf_program fp;
    bpf_u_int32 mask;
    bpf_u_int32 net;
    char errbuf[PCAP_ERRBUF_SIZE];
    
    // 获取网络地址和掩码
    if (pcap_lookupnet(m_deviceName.c_str(), &net, &mask, errbuf) == -1) {
        // printf("警告: 无法获取网络信息: %s\n", errbuf);
        net = 0;
        mask = 0;
    }
    
    // 编译过滤器
    if (pcap_compile(m_handle, &fp, filterExpression.c_str(), 0, net) == -1) {
        m_lastError = "编译过滤器失败: " + string(pcap_geterr(m_handle));
        return false;
    }
    
    // 设置过滤器
    if (pcap_setfilter(m_handle, &fp) == -1) {
        m_lastError = "设置过滤器失败: " + string(pcap_geterr(m_handle));
        pcap_freecode(&fp);
        return false;
    }
    
    pcap_freecode(&fp);
    m_filterExpression = filterExpression;
    // printf("PacketCapture: 设置过滤器: %s\n", filterExpression.c_str());
    
    return true;
}

bool PacketCapture::parseEthernetHeader(const u_char* packet, NetworkPacketData* packetData)
{
    struct ether_header* eth_header = (struct ether_header*)packet;
    
    // 解析MAC地址
    packetData->srcMAC = macToString(eth_header->ether_shost);
    packetData->dstMAC = macToString(eth_header->ether_dhost);
    
    // 检查是否为IP数据包
    if (ntohs(eth_header->ether_type) == ETHERTYPE_IP) {
        return parseIPHeader(packet, sizeof(struct ether_header), packetData);
    }
    
    // 其他类型的数据包
    packetData->protocol = "OTHER";
    return true;
}

string PacketCapture::macToString(const u_char* mac)
{
    stringstream ss;
    ss << hex << setfill('0');
    for (int i = 0; i < 6; i++) {
        if (i > 0) ss << ":";
        ss << setw(2) << (int)mac[i];
    }
    return ss.str();
}

string PacketCapture::ipToString(uint32_t ip)
{
    struct in_addr addr;
    addr.s_addr = ip;
    return string(inet_ntoa(addr));
}

bool PacketCapture::parseIPHeader(const u_char* packet, int offset, NetworkPacketData* packetData)
{
    struct ip* ip_header = (struct ip*)(packet + offset);

    // 解析IP地址
    packetData->srcIP = ipToString(ip_header->ip_src.s_addr);
    packetData->dstIP = ipToString(ip_header->ip_dst.s_addr);

    // 获取IP头长度
    int ip_header_len = ip_header->ip_hl * 4;

    // 根据协议类型解析传输层头
    switch (ip_header->ip_p) {
        case IPPROTO_TCP:
            packetData->protocol = "TCP";
            return parseTCPHeader(packet, offset + ip_header_len, packetData);
        case IPPROTO_UDP:
            packetData->protocol = "UDP";
            return parseUDPHeader(packet, offset + ip_header_len, packetData);
        case IPPROTO_ICMP:
            packetData->protocol = "ICMP";
            break;
        default:
            packetData->protocol = "IP";
            break;
    }

    return true;
}

bool PacketCapture::parseTCPHeader(const u_char* packet, int offset, NetworkPacketData* packetData)
{
    struct tcphdr* tcp_header = (struct tcphdr*)(packet + offset);

    packetData->srcPort = ntohs(tcp_header->th_sport);
    packetData->dstPort = ntohs(tcp_header->th_dport);

    return true;
}

bool PacketCapture::parseUDPHeader(const u_char* packet, int offset, NetworkPacketData* packetData)
{
    struct udphdr* udp_header = (struct udphdr*)(packet + offset);

    packetData->srcPort = ntohs(udp_header->uh_sport);
    packetData->dstPort = ntohs(udp_header->uh_dport);

    return true;
}

