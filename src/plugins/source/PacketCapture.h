//
//  PacketCapture.h
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef PacketCapture_h
#define PacketCapture_h

#include "common/common.h"
#include "common/msg.h"
#include <pcap.h>
#include <netinet/in.h>
#include <netinet/if_ether.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/udp.h>
#include <arpa/inet.h>

using namespace std;

/**
 * 网卡抓包工具类
 * 封装libpcap的网卡抓包功能
 */
class PacketCapture
{
public:
    PacketCapture();
    ~PacketCapture();
    
    // 初始化网卡设备
    bool initDevice(const string& deviceName = "", const string& filter = "");
    
    // 开始抓包
    bool startCapture();
    
    // 停止抓包
    void stopCapture();
    
    // 获取下一个数据包
    NetworkPacketData* getNextPacket();
    
    // 获取可用网卡列表
    vector<string> getAvailableDevices();
    
    // 设置过滤器
    bool setFilter(const string& filterExpression);
    
    // 获取错误信息
    string getLastError() const { return m_lastError; }
    
    // 获取统计信息
    struct CaptureStats {
        int packetsReceived;
        int packetsDropped;
        int packetsTotal;
    };
    CaptureStats getStats() const { return m_stats; }

private:
    pcap_t* m_handle;                   // pcap句柄
    string m_deviceName;                // 网卡设备名
    string m_filterExpression;          // 过滤表达式
    string m_lastError;                 // 最后的错误信息
    bool m_isCapturing;                 // 是否正在抓包
    CaptureStats m_stats;               // 统计信息
    
    // 解析以太网头
    bool parseEthernetHeader(const u_char* packet, NetworkPacketData* packetData);
    
    // 解析IP头
    bool parseIPHeader(const u_char* packet, int offset, NetworkPacketData* packetData);
    
    // 解析TCP头
    bool parseTCPHeader(const u_char* packet, int offset, NetworkPacketData* packetData);
    
    // 解析UDP头
    bool parseUDPHeader(const u_char* packet, int offset, NetworkPacketData* packetData);
    
    // MAC地址转字符串
    string macToString(const u_char* mac);
    
    // IP地址转字符串
    string ipToString(uint32_t ip);
};

#endif /* PacketCapture_h */
