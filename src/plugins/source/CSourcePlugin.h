//
//  Source.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef CSourcePlugin_hpp
#define CSourcePlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CSourceTasker.h"
#include "PacketCapture.h"
#include <memory>

class CSourcePlugin : public IPlugin
{
public:
    CSourcePlugin();
    virtual ~CSourcePlugin();
    virtual PluginInfo* info() override;

    // handle方法已移除 - Tasker会直接将消息传递给下游的队列

    // 重写控制接口（使用新的签名）
    virtual void control(int msgID, void* controlData = nullptr) override {
        if (msgID == 301 && controlData) {
            SourceInteractionMsg *src_mo_msg = (SourceInteractionMsg*)controlData;
            printf("control msg source 301: command=%s, params=%s\n",
                   src_mo_msg->command.c_str(), src_mo_msg->parameters.c_str());
        } else {
            printf("Source: 收到控制消息 msgID=%d\n", msgID);
        }
    }

    virtual int telemetry(void *msg) override {
        printf("Source::telemetry() - 返回数据源插件遥测数据\n");

        // 需要包含 TelemetryData 结构体定义
        // 这里暂时使用简单的处理方式
        if (msg) {
            // 假设 msg 指向 TelemetryData 结构
            printf("Source: 提供遥测数据 - 状态:running, 消息数:%d\n", rand() % 500);
        }

        return 0;
    }

    virtual void init() override;
    virtual void uninit() override;
    virtual void start() override;
    virtual void stop() override;
    void packet_capture_loop();
    static void* packet_capture_wrapper(void* arg);

private:
    pthread_t m_thread_id;
    PacketCapture* m_packetCapture;
    CSourceTasker* m_tasker;
    bool m_isRunning;
    string m_deviceName;
    string m_filterExpression;
};

#endif /* DemoSource_hpp */