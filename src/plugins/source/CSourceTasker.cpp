//
//  CSourceTasker.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CSourceTasker.h"
#include "common/MessageFactory.h"
#include <mutex>
#include <sstream>

// 全局互斥锁，用于保护NetworkPacketData的访问
static std::mutex g_packet_mutex;

// 前向声明
class PluginManager;

CSourceTasker::CSourceTasker(IPlugin *plugin): m_plugin(plugin)
{
    // printf("new CSourceTasker \n");
    (void)m_plugin;  // 避免未使用变量警告
}

CSourceTasker::~CSourceTasker()
{
    // printf("delete CSourceTasker  \n");
}

void* CSourceTasker::execute(void *msg)
{
    static int packetCount = 0; // 确保在函数作用域内声明

    if(!msg)
    {
        usleep(1000000);
        return nullptr;
    }

    // 使用互斥锁保护对NetworkPacketData的访问
    std::lock_guard<std::mutex> lock(g_packet_mutex);

    // 处理网络数据包消息
    NetworkPacketData *packetData = (NetworkPacketData*)msg;

    // 数据包计数
    packetCount++;

    // 添加严格的安全检查
    if (!packetData) {
        // printf("CSourceHandler: 错误 - 接收到空的数据包指针\n");
        return nullptr;
    }

    // 检查数据包大小的合理性
    if (packetData->packetSize == 0 || packetData->packetSize > 65536) {
        delete packetData; // 无效数据包，直接释放
        return nullptr;
    }

    // 检查数据指针的有效性
    if (!packetData->packetData) {
        delete packetData; // 无效数据包，直接释放
        return nullptr;
    }



    // HTTP解析和输出
    if (packetData->protocol == "TCP" &&
        (packetData->dstPort == 80 || packetData->srcPort == 80 ||
         packetData->dstPort == 443 || packetData->srcPort == 443)) {



        // 解析HTTP数据包
        parseHTTPPacket(packetData);
    }

    // 处理完成后删除数据包
    delete packetData;

    // 生成控制流监控消息
    packetCount++;

    // 使用消息工厂创建遥测消息，自动填充msgID
    SourceMonitorMsg* control_msg = MessageFactory::createSourceTelemetryMsg(packetCount, 0.5, 1024);

    // 暂时删除消息避免内存泄漏
    delete control_msg;

    return nullptr;  // Source目前不返回处理结果，直接在parseHTTPRequest中输出
}

void CSourceTasker::clear()
{
    return;
}

// HTTP数据包解析方法
void CSourceTasker::parseHTTPPacket(NetworkPacketData* packetData)
{
    if (!packetData || !packetData->packetData || packetData->packetSize == 0) {
        return;
    }

    // 跳过以太网头部(14字节)、IP头部和TCP头部，找到HTTP数据
    unsigned char* data = (unsigned char*)packetData->packetData;
    size_t httpDataOffset = 0;

    // 跳过以太网头部(14字节)
    if (packetData->packetSize > 14) {
        httpDataOffset = 14;

        // 检查IP头部长度(IP头部第一个字节的低4位 * 4)
        if (packetData->packetSize > httpDataOffset) {
            unsigned char ipHeaderLen = (data[httpDataOffset] & 0x0F) * 4;
            httpDataOffset += ipHeaderLen;

            // 检查TCP头部长度(TCP头部第12个字节的高4位 * 4)
            if (packetData->packetSize > httpDataOffset + 12) {
                unsigned char tcpHeaderLen = ((data[httpDataOffset + 12] >> 4) & 0x0F) * 4;
                httpDataOffset += tcpHeaderLen;
            }
        }
    }

    // 确保偏移量有效
    if (httpDataOffset >= packetData->packetSize) {
        return;
    }

    // 从HTTP数据开始创建字符串
    string packetContent((char*)(data + httpDataOffset), packetData->packetSize - httpDataOffset);

    // 查找HTTP请求行
    size_t getPos = packetContent.find("GET ");
    size_t postPos = packetContent.find("POST ");
    size_t putPos = packetContent.find("PUT ");
    size_t deletePos = packetContent.find("DELETE ");

    // 检查是否是HTTP请求
    if (getPos != string::npos || postPos != string::npos ||
        putPos != string::npos || deletePos != string::npos) {

        // 解析HTTP请求
        parseHTTPRequest(packetContent, packetData);
    }
}

// 解析HTTP请求
void CSourceTasker::parseHTTPRequest(const string& content, NetworkPacketData* packetData)
{
    istringstream stream(content);
    string line;

    // 读取请求行
    if (getline(stream, line)) {
        istringstream requestLine(line);
        string method, url, version;

        if (requestLine >> method >> url >> version) {
            // 解析Host头部
            string host = "unknown";
            string hostLine;
            while (getline(stream, hostLine)) {
                if (hostLine.find("Host:") == 0) {
                    host = hostLine.substr(5);
                    // 去除前后空格和回车符
                    host.erase(0, host.find_first_not_of(" \t\r\n"));
                    host.erase(host.find_last_not_of(" \t\r\n") + 1);
                    break;
                }
            }

            // 输出格式化的HTTP请求信息
            printf("HTTP请求 - Method: %s, URL: %s, Host: %s\n",
                   method.c_str(), url.c_str(), host.c_str());
        }
    }
}

// 解析HTTP响应
void CSourceTasker::parseHTTPResponse(const string& content, NetworkPacketData* packetData)
{
    istringstream stream(content);
    string line;

    // 读取状态行
    if (getline(stream, line)) {
        istringstream statusLine(line);
        string version, statusCode, statusMessage;

        if (statusLine >> version >> statusCode) {
            // 读取剩余的状态消息
            getline(statusLine, statusMessage);
            printf("HTTP响应 - Status: %s, Message: %s%s\n",
                   statusCode.c_str(), statusMessage.c_str(), "");
        }
    }
}