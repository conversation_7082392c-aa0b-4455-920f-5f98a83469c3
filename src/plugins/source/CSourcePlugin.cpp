//
//  DemoSource.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CSourcePlugin.h"
#include "3rd/json/json.hpp"
#include <fstream>

using json = nlohmann::json;

CSourcePlugin::CSourcePlugin()
{
    m_info = new PluginInfo("./config/source.json");
    m_packetCapture = new PacketCapture();
    m_tasker = nullptr;
    m_isRunning = false;
    m_deviceName = "";  // 使用配置文件中的设备或默认网卡
    m_filterExpression = "";  // 不设置过滤器，捕获所有数据包
}

CSourcePlugin::~CSourcePlugin()
{
    // printf("delete CSourcePlugin \n");

    stop();  // 确保停止抓包

    delete m_info;
    delete m_packetCapture;
    if (m_tasker) {
        delete m_tasker;
        m_tasker = nullptr;
    }

    sleep(1);  // 使用 C 风格的 sleep
    if (m_isRunning) {
        pthread_cancel(m_thread_id);
    }
}

PluginInfo* CSourcePlugin::info()
{
    return m_info;
}

// handle方法已移除 - Tasker会直接将消息传递给下游的队列

void CSourcePlugin::init()
{
    // printf("CSourcePlugin::init() - 初始化网卡抓包插件\n");

    // 从配置文件读取网卡设置
    try {
        ifstream config_file("./config/source.json");
        if (config_file.is_open()) {
            json config;
            config_file >> config;

            if (config.contains("capture")) {
                auto capture_config = config["capture"];
                if (capture_config.contains("device") && !capture_config["device"].get<string>().empty()) {
                    m_deviceName = capture_config["device"].get<string>();
                }
                if (capture_config.contains("filter") && !capture_config["filter"].get<string>().empty()) {
                    m_filterExpression = capture_config["filter"].get<string>();
                }
            }
            config_file.close();
        }
    } catch (const exception& e) {
        printf("警告: 读取配置文件失败: %s\n", e.what());
    }

    // printf("使用网卡设备: %s\n", m_deviceName.empty() ? "默认" : m_deviceName.c_str());
    // printf("使用过滤器: %s\n", m_filterExpression.empty() ? "无" : m_filterExpression.c_str());

    // 显示可用网卡设备
    // vector<string> devices = m_packetCapture->getAvailableDevices();
    // printf("可用网卡设备:\n");
    // for (const auto& device : devices) {
    //     printf("  - %s\n", device.c_str());
    // }

    // 显示可用网卡设备
    vector<string> devices = m_packetCapture->getAvailableDevices();
    printf("可用网卡设备:\n");
    for (const auto& device : devices) {
        printf("  - %s\n", device.c_str());
    }

    // 初始化网卡设备
    printf("CSourcePlugin: 使用网卡设备: %s\n", m_deviceName.c_str());
    if (!m_packetCapture->initDevice(m_deviceName, m_filterExpression)) {
        printf("错误: 初始化网卡设备失败: %s\n", m_packetCapture->getLastError().c_str());
        printf("提示: 可能需要管理员权限来访问网络接口\n");
        return;
    }

    printf("CSourcePlugin: 网卡抓包插件初始化完成\n");

    // 创建 Tasker（在 initDI() 之前创建，这样 setup() 时会自动注入）
    m_tasker = new CSourceTasker(this);
}

void CSourcePlugin::uninit()
{
    // printf("CSourcePlugin::uninit() - 清理网卡抓包插件\n");
    stop();
}

void CSourcePlugin::start()
{
    // Tasker 已在 init() 中创建，并在 initDI() 中自动注入到 Worker

    // 检查网卡设备是否已初始化
    if (!m_packetCapture) {
        printf("错误: PacketCapture 对象未初始化\n");
        return;
    }

    // 开始抓包
    if (!m_packetCapture->startCapture()) {
        printf("错误: 启动网卡抓包失败: %s\n", m_packetCapture->getLastError().c_str());
        printf("提示: 可能需要管理员权限来访问网络接口\n");
        printf("提示: 请尝试使用 sudo 运行程序\n");
        return;
    }

    m_isRunning = true;

    // 启动数据包捕获线程
    int result = pthread_create(&m_thread_id, NULL, (void*(*)(void*))&CSourcePlugin::packet_capture_wrapper, this);
    if (result != 0) {
        printf("错误: 创建数据包捕获线程失败，错误码: %d\n", result);
        m_isRunning = false;
        m_packetCapture->stopCapture();
        return;
    }


    return;
}

void CSourcePlugin::stop()
{
    // printf("CSourcePlugin::stop() - 停止网卡抓包\n");

    m_isRunning = false;

    if (m_packetCapture) {
        m_packetCapture->stopCapture();
    }

    // printf("CSourcePlugin: 网卡抓包已停止\n");
}

// pthread 包装函数
void* CSourcePlugin::packet_capture_wrapper(void* arg)
{
    CSourcePlugin* source = (CSourcePlugin*)arg;
    source->packet_capture_loop();
    return NULL;
}

void CSourcePlugin::packet_capture_loop()
{

    while (m_isRunning)
    {
        // 获取下一个数据包
        NetworkPacketData* packetData = m_packetCapture->getNextPacket();
        if (packetData == nullptr) {
            // 没有数据包或出错，短暂休眠后继续
            usleep(1000);  // 1ms
            continue;
        }

        // 减少输出量，只显示HTTP流量 (已注释)
        // if (packetData->dstPort == 80 || packetData->srcPort == 80) {
        //     printf("CSourcePlugin: 捕获HTTP数据包 %s:%d -> %s:%d (%s, %zu bytes)\n",
        //            packetData->srcIP.c_str(), packetData->srcPort,
        //            packetData->dstIP.c_str(), packetData->dstPort,
        //            packetData->protocol.c_str(), packetData->packetSize);
        // }

        // 将数据包发送给处理器
        if (m_downstream_queue) {
            m_downstream_queue->produce(packetData);
        } else {
            printf("CSourcePlugin: 下游队列为空，删除数据包\n");
            delete packetData;
        }
    }
}

