//
//  CHandler.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef C_Source_Tasker_h
#define C_Source_Tasker_h

#include "common/common.h"
#include "common/msg.h"
#include "common/interactorMsg.h"
#include "utils/utils.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"

using namespace std;

class CSourceTasker : public ITasker
{
public:
    CSourceTasker(IPlugin *plugin);
    virtual ~CSourceTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;
private:
    IPlugin *m_plugin;

    // HTTP解析相关方法
    void parseHTTPPacket(NetworkPacketData* packetData);
    void parseHTTPRequest(const string& content, NetworkPacketData* packetData);
    void parseHTTPResponse(const string& content, NetworkPacketData* packetData);
};

#endif /* C_Source_Tasker_h */