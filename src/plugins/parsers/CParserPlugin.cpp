//
//  Parser.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/7.
//

#include "CParserPlugin.h"

CParserPlugin::CParserPlugin()
{
    m_info = new PluginInfo("./config/parser.json");
}

CParserPlugin::~CParserPlugin()
{
    printf("delete CPlugin \n");
    delete m_info;
}

PluginInfo* CParserPlugin::info()
{
    return m_info;
}

// handle方法已移除 - Tasker会直接将消息传递给下游的队列

void CParserPlugin::init()
{
    // 创建 Tasker（在 initDI() 之前创建，这样 setup() 时会自动注入）
    m_tasker = new CParserTasker(this);
}

// start() 和 stop() 方法已移除，使用父类 IPlugin 的默认实现

// 已删除：processMessage 方法不再需要
// Worker 会直接调用 Tasker->excute(msg) 处理消息
