//
//  Parser.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/7.
//

#include "CParserPlugin.h"

CParserPlugin::CParserPlugin()
{
    m_info = new PluginInfo("./config/parser.json");
}

CParserPlugin::~CParserPlugin()
{
    printf("delete CPlugin \n");
    delete m_info;
}

PluginInfo* CParserPlugin::info()
{
    return m_info;
}

// handle方法已移除 - Tasker会直接将消息传递给下游的队列

void CParserPlugin::init()
{
    // 创建 Tasker（在 initDI() 之前创建，这样 setup() 时会自动注入）
    m_tasker = new CParserTasker(this);
}

void CParserPlugin::start()
{
    // Tasker 已在 init() 中创建，并在 initDI() 中自动注入到 Worker
}

void CParserPlugin::stop()
{
    // IPlugin 的析构函数会处理工作线程的清理
}

// 已删除：processMessage 方法不再需要
// Worker 会直接调用 Tasker->excute(msg) 处理消息
