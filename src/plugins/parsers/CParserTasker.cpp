//
//  CParserTasker.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CParserTasker.h"
#include <netinet/in.h>
#include <netinet/if_ether.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>

// 前向声明
class PluginManager;

CParserTasker::CParserTasker(IPlugin *plugin): m_plugin(plugin)
{
    // printf("new CParserTasker \n");
    m_tcpSessionManager = new TCPSessionManager();
    m_httpParser = new HTTPProtocolParser();
}

CParserTasker::~CParserTasker()
{
    // printf("delete CParserTasker  \n");
    delete m_tcpSessionManager;
    delete m_httpParser;
}

void* CParserTasker::execute(void *msg)
{
    // printf("CParserHandler::execute() - 接收到消息，地址: %p\n", msg);

    if(!msg)
    {
        printf("CParserHandler::execute() - 消息为空\n");
        usleep(1000000);
        return nullptr;
    }

    // 尝试识别消息类型
    // 首先尝试将消息转换为NetworkPacketData
    NetworkPacketData *networkData = (NetworkPacketData*)msg;

    // printf("CParserHandler::execute() - 尝试转换为NetworkPacketData，地址: %p\n", networkData);

    // 添加安全检查，确保指针有效且数据合理
    bool isNetworkPacket = false;
    try {
        // 检查是否可能是NetworkPacketData
        if (networkData && networkData->packetSize > 0 && networkData->packetSize < 65536 &&
            networkData->packetData != nullptr) {
            // 尝试访问协议字段
            string protocol = networkData->protocol;
            // printf("CParserHandler::execute() - 协议: %s, 数据包大小: %zu\n",
            //        protocol.c_str(), networkData->packetSize);
            if (protocol == "TCP" || protocol == "UDP") {
                isNetworkPacket = true;
                // printf("CParserHandler::execute() - 确认为网络数据包消息\n");
            } else {
                // printf("CParserHandler::execute() - 协议类型不匹配: '%s'\n", protocol.c_str());
            }
        } else {
            // printf("CParserHandler::execute() - 数据包大小无效或指针为空\n");
        }
    } catch (...) {
        // 如果访问失败，说明不是有效的NetworkPacketData
        // printf("CParserHandler::execute() - 访问NetworkPacketData失败，可能不是网络数据包消息\n");
        isNetworkPacket = false;
    }

    if (isNetworkPacket) {
        // 显示所有TCP数据包用于调试 (已注释)
        // if (networkData->protocol == "TCP") {
        //     printf("CParserHandler: 接收到TCP数据包 %s:%d -> %s:%d (%zu bytes)\n",
        //            networkData->srcIP.c_str(), networkData->srcPort,
        //            networkData->dstIP.c_str(), networkData->dstPort,
        //            networkData->packetSize);
        // }

        // 处理网络数据包进行HTTP协议解析
        try {
            processNetworkPacket(networkData);
        } catch (...) {
            printf("CParserHandler: processNetworkPacket 异常\n");
            // 不在这里删除，让processNetworkPacket自己管理内存
        }
        return nullptr;  // Parser处理网络数据包，暂时不返回结果
    }

    // 处理传统的MyMsg消息（向后兼容）
    MyMsg *m = (MyMsg*)msg;
    // printf("CParserHandler: 接收到传统数据流消息 (数据值: %d) 开始解析\n", m->m_i);
    // 移除sleep(2)以避免阻塞网络数据包处理

    // 生成解析后的数据流消息（ID 21）发送给 upload 插件
    MyMsg *data_msg = new MyMsg{21, 2, nullptr};
    // printf("CParserHandler: 传统消息解析完成，生成数据流消息 (数据值: %d) 发送给 upload\n", data_msg->m_i);

    // 暂时删除消息，在实际项目中应该通过消息队列发送
    delete data_msg;

    // 处理完成后删除传统消息
    delete m;
    return nullptr;  // Parser处理传统消息，暂时不返回结果
}

void CParserTasker::clear()
{
    return;
}

void CParserTasker::processNetworkPacket(const NetworkPacketData* networkData)
{
    // printf("CParserHandler: 解析数据包头部成功 - %s:%d -> %s:%d (%s)\n",
    //        networkData->srcIP.c_str(), networkData->srcPort,
    //        networkData->dstIP.c_str(), networkData->dstPort,
    //        networkData->protocol.c_str());

    // 只处理TCP数据包
    if (networkData->protocol != "TCP") {
        // printf("CParserHandler: 跳过非TCP数据包 (协议: %s)\n", networkData->protocol.c_str());
        return;
    }

    // 简化HTTP检测 - 检查所有TCP数据包中的HTTP请求
    if (networkData->protocol == "TCP") {
        // 尝试从TCP载荷中提取HTTP数据
        // 跳过以太网头(14字节) + IP头(至少20字节) + TCP头(至少20字节)
        size_t minHeaderSize = 14 + 20 + 20;  // 最小头部大小
        if (networkData->packetSize > minHeaderSize + 10) {  // 至少要有一些载荷
            // 简化处理：假设标准头部大小，直接跳到TCP载荷
            const char* tcpPayload = (const char*)networkData->packetData + minHeaderSize;
            size_t payloadSize = networkData->packetSize - minHeaderSize;

            if (payloadSize > 10) {  // 至少要有一些数据
                // 创建安全的字符串，避免内存问题
                string httpData;
                try {
                    httpData.assign(tcpPayload, min(payloadSize, (size_t)1024));  // 限制最大1KB
                } catch (...) {
                    printf("CParserHandler: 创建HTTP数据字符串失败\n");
                    // 不在这里删除，在函数末尾统一删除
                    return;
                }

                // 简单检查是否为HTTP请求
                if (isSimpleHTTPRequest(httpData)) {
                    // 提取HTTP方法和URL
                    string method, url, fullUrl;
                    if (extractHTTPInfo(httpData, networkData->dstIP, networkData->dstPort, method, url, fullUrl)) {
                        printf("HTTP %s %s\n", method.c_str(), fullUrl.c_str());

                        // 创建简化的HTTP解析结果
                        HTTPParseResult* parseResult = createSimpleHTTPResult(networkData, method, url, fullUrl);

                        if (parseResult) {
                            // 已简化：不再需要手动发送到队列
                            // Worker 会自动处理消息流
                            printf("CParserHandler: HTTP解析完成，结果: %s\n", parseResult->url.c_str());

                            // 暂时删除解析结果，在实际实现中应该由 Worker 处理
                            delete parseResult;
                        }
                    }
                }
            }
        }
    }

    // 注意：不在这里删除 networkData，由调用方 excute() 负责删除
    // delete networkData;  // 移除重复删除
}

void CParserTasker::processTCPStreams()
{
    // 获取重组后的TCP流
    vector<pair<TCPConnection, string>> streams = m_tcpSessionManager->getReassembledStreams();

    printf("CParserHandler: 获取到 %zu 个重组后的TCP流\n", streams.size());

    for (const auto& stream : streams) {
        const TCPConnection& conn = stream.first;
        const string& data = stream.second;

        // printf("CParserHandler: 处理重组TCP流 %s:%d -> %s:%d (大小: %zu bytes)\n",
        //        conn.srcIP.c_str(), conn.srcPort, conn.dstIP.c_str(), conn.dstPort, data.size());

        // 检查是否为HTTP流量
        if (m_httpParser->isHTTPTraffic(data)) {
            printf("CParserHandler: 检测到HTTP流量，开始解析\n");

            // 解析HTTP消息
            vector<HTTPMessage> httpMessages = m_httpParser->parseHTTPStream(conn, data);

            for (const HTTPMessage& httpMsg : httpMessages) {
                if (httpMsg.isValid) {
                    // 转换为解析结果并发送给upload
                    HTTPParseResult* parseResult = convertToParseResult(httpMsg);

                    if (parseResult) {
                        // 已简化：不再需要手动发送到队列
                        // Worker 会自动处理消息流
                        printf("CParserHandler: TCP流HTTP解析完成，结果: %s\n", parseResult->url.c_str());

                        // 暂时删除解析结果，在实际实现中应该由 Worker 处理
                        delete parseResult;
                    }
                }
            }
        } else {
            printf("CParserHandler: 非HTTP流量，跳过解析 (数据: %s)\n", data.substr(0, 50).c_str());
        }
    }
    // 清理过期会话
    m_tcpSessionManager->cleanupExpiredSessions();
}


HTTPParseResult* CParserTasker::convertToParseResult(const HTTPMessage& httpMsg)
{
    HTTPParseResult* result = new HTTPParseResult();

    // 连接信息
    result->srcIP = httpMsg.connection.srcIP;
    result->dstIP = httpMsg.connection.dstIP;
    result->srcPort = httpMsg.connection.srcPort;
    result->dstPort = httpMsg.connection.dstPort;
    result->timestamp = httpMsg.timestamp;

    // HTTP消息类型
    result->messageType = (int)httpMsg.type;

    // HTTP请求信息
    result->method = httpMsg.methodStr;
    result->url = httpMsg.url;
    result->path = httpMsg.path;
    result->query = httpMsg.query;
    result->httpVersion = httpMsg.httpVersion;

    // HTTP响应信息
    result->statusCode = httpMsg.statusCode;
    result->statusMessage = httpMsg.statusMessage;

    // HTTP头部信息（转换为JSON字符串）
    result->headers = mapToJsonString(httpMsg.headers);

    // HTTP消息体
    result->body = httpMsg.body;
    result->contentLength = httpMsg.contentLength;

    // 解析元数据
    result->isComplete = httpMsg.isComplete;
    result->isValid = httpMsg.isValid;

    return result;
}

string CParserTasker::mapToJsonString(const map<string, string>& headers)
{
    json j;
    for (const auto& pair : headers) {
        j[pair.first] = pair.second;
    }
    return j.dump();
}

// 简化的HTTP请求检测
bool CParserTasker::isSimpleHTTPRequest(const string& data) {
    // 检查是否以HTTP方法开头
    if (data.size() < 10) return false;

    return (data.substr(0, 4) == "GET " ||
            data.substr(0, 5) == "POST " ||
            data.substr(0, 4) == "PUT " ||
            data.substr(0, 7) == "DELETE " ||
            data.substr(0, 5) == "HEAD " ||
            data.substr(0, 8) == "OPTIONS " ||
            data.substr(0, 6) == "PATCH ");
}

// 提取HTTP信息
bool CParserTasker::extractHTTPInfo(const string& data, const string& dstIP, int dstPort,
                                    string& method, string& url, string& fullUrl) {
    size_t firstSpace = data.find(' ');
    if (firstSpace == string::npos) return false;

    method = data.substr(0, firstSpace);

    size_t secondSpace = data.find(' ', firstSpace + 1);
    if (secondSpace == string::npos) return false;

    url = data.substr(firstSpace + 1, secondSpace - firstSpace - 1);

    // 构建完整URL
    string protocol = (dstPort == 443) ? "https" : "http";
    string portStr = ((dstPort == 80 && protocol == "http") || (dstPort == 443 && protocol == "https"))
                     ? "" : ":" + to_string(dstPort);

    fullUrl = protocol + "://" + dstIP + portStr + url;

    return true;
}

// 创建简化的HTTP解析结果
HTTPParseResult* CParserTasker::createSimpleHTTPResult(const NetworkPacketData* networkData,
                                                       const string& method, const string& url, const string& fullUrl) {
    HTTPParseResult* result = new HTTPParseResult();

    // 连接信息
    result->timestamp = networkData->timestamp;
    result->srcIP = networkData->srcIP;
    result->srcPort = networkData->srcPort;
    result->dstIP = networkData->dstIP;
    result->dstPort = networkData->dstPort;

    // HTTP请求信息
    result->messageType = 0;  // 0=请求
    result->method = method;
    result->url = fullUrl;    // 使用完整URL
    result->path = url;       // 原始路径
    result->query = "";       // 简化版本不解析查询参数
    result->httpVersion = "HTTP/1.1";  // 默认版本

    // HTTP响应信息（请求时为空）
    result->statusCode = 0;
    result->statusMessage = "";

    // HTTP头部和消息体
    result->headers = "{}";   // 空JSON对象
    result->body = "";
    result->contentLength = 0;

    // 解析元数据
    result->isComplete = true;
    result->isValid = true;

    return result;
}

