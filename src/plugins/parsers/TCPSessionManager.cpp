//
//  TCPSessionManager.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "TCPSessionManager.h"
#include <algorithm>
#include <cstring>
#include <netinet/if_ether.h>
#include <arpa/inet.h>

TCPSessionManager::TCPSessionManager() {
    // printf("TCPSessionManager: 初始化TCP会话管理器\n");
}

TCPSessionManager::~TCPSessionManager() {
    // 清理所有会话
    for (auto& pair : m_sessions) {
        delete pair.second;
    }
    m_sessions.clear();
    // printf("TCPSessionManager: 清理TCP会话管理器\n");
}

bool TCPSessionManager::processPacket(const NetworkPacketData* packet) {
    if (!packet || packet->protocol != "TCP") {
        return false;
    }
    
    // 解析TCP头部
    uint32_t seqNum, ackNum;
    uint16_t flags;
    size_t headerLen;
    const void* payload;
    size_t payloadSize;
    
    if (!parseTCPHeader(packet->packetData, packet->packetSize, 
                       seqNum, ackNum, flags, headerLen, payload, payloadSize)) {
        return false;
    }
    
    // 创建连接标识
    TCPConnection conn = {packet->srcIP, packet->srcPort, packet->dstIP, packet->dstPort};
    
    // 获取或创建会话
    TCPSession* session = getOrCreateSession(conn);
    if (!session) {
        return false;
    }
    
    // 创建TCP数据段
    TCPSegment* segment = new TCPSegment(seqNum, ackNum, flags, payloadSize, 
                                        const_cast<void*>(payload), packet->timestamp);
    
    // 判断数据流方向
    bool isClientToServer = this->isClientToServer(session->connection, conn);
    
    // 处理TCP数据段
    processTCPSegment(session, segment, isClientToServer);
    
    // 更新会话状态
    updateSessionState(session, flags, isClientToServer);
    
    return true;
}

bool TCPSessionManager::parseTCPHeader(const void* data, size_t dataSize,
                                      uint32_t& seqNum, uint32_t& ackNum, uint16_t& flags,
                                      size_t& headerLen, const void*& payload, size_t& payloadSize) {
    if (dataSize < sizeof(struct ether_header) + sizeof(struct ip) + sizeof(struct tcphdr)) {
        return false;
    }
    
    // 跳过以太网头部
    const u_char* ptr = (const u_char*)data + sizeof(struct ether_header);
    
    // 解析IP头部
    const struct ip* ipHeader = (const struct ip*)ptr;
    size_t ipHeaderLen = ipHeader->ip_hl * 4;
    ptr += ipHeaderLen;
    
    // 解析TCP头部
    const struct tcphdr* tcpHeader = (const struct tcphdr*)ptr;
    seqNum = ntohl(tcpHeader->th_seq);
    ackNum = ntohl(tcpHeader->th_ack);
    flags = tcpHeader->th_flags;
    headerLen = tcpHeader->th_off * 4;
    
    // 计算payload
    ptr += headerLen;
    size_t totalHeaderSize = sizeof(struct ether_header) + ipHeaderLen + headerLen;
    
    if (dataSize > totalHeaderSize) {
        payload = ptr;
        payloadSize = dataSize - totalHeaderSize;
    } else {
        payload = nullptr;
        payloadSize = 0;
    }
    
    return true;
}

TCPSession* TCPSessionManager::getOrCreateSession(const TCPConnection& conn) {
    // 首先查找正向连接
    auto it = m_sessions.find(conn);
    if (it != m_sessions.end()) {
        it->second->lastActivity = (int)time(nullptr);
        return it->second;
    }
    
    // 查找反向连接
    TCPConnection reverseConn = conn.reverse();
    it = m_sessions.find(reverseConn);
    if (it != m_sessions.end()) {
        it->second->lastActivity = (int)time(nullptr);
        return it->second;
    }
    
    // 创建新会话
    TCPSession* session = new TCPSession(conn);
    session->lastActivity = (int)time(nullptr);
    m_sessions[conn] = session;
    
    // printf("TCPSessionManager: 创建新TCP会话 %s:%d -> %s:%d\n",
    //        conn.srcIP.c_str(), conn.srcPort, conn.dstIP.c_str(), conn.dstPort);
    
    return session;
}

void TCPSessionManager::processTCPSegment(TCPSession* session, const TCPSegment* segment, bool isClientToServer) {
    // 根据方向添加到相应的数据段列表
    if (isClientToServer) {
        session->clientSegments.push_back(const_cast<TCPSegment*>(segment));
        
        // 按序列号排序
        sort(session->clientSegments.begin(), session->clientSegments.end(),
             [](const TCPSegment* a, const TCPSegment* b) {
                 return a->seqNum < b->seqNum;
             });
    } else {
        session->serverSegments.push_back(const_cast<TCPSegment*>(segment));
        
        // 按序列号排序
        sort(session->serverSegments.begin(), session->serverSegments.end(),
             [](const TCPSegment* a, const TCPSegment* b) {
                 return a->seqNum < b->seqNum;
             });
    }
    
    // 如果有数据，尝试重组
    if (segment->dataSize > 0) {
        // printf("TCPSessionManager: 收到TCP数据段 seq=%u, size=%zu, 方向=%s\n",
        //        segment->seqNum, segment->dataSize, isClientToServer ? "C->S" : "S->C");
        
        // 检查是否可以重组完整的应用层数据
        string reassembledData;
        if (isClientToServer) {
            reassembledData = reassembleStream(session->clientSegments);
        } else {
            reassembledData = reassembleStream(session->serverSegments);
        }
        
        if (!reassembledData.empty()) {
            m_reassembledStreams.push_back({session->connection, reassembledData});
            // printf("TCPSessionManager: 重组完成，数据大小: %zu bytes\n", reassembledData.size());
        }
    }
}

string TCPSessionManager::reassembleStream(const vector<TCPSegment*>& segments) {
    if (segments.empty()) {
        return "";
    }
    
    string result;
    uint32_t expectedSeq = segments[0]->seqNum;
    
    for (const auto& segment : segments) {
        if (segment->dataSize == 0) {
            continue;
        }
        
        // 检查序列号连续性
        if (segment->seqNum == expectedSeq) {
            result.append((char*)segment->data, segment->dataSize);
            expectedSeq += segment->dataSize;
        } else if (segment->seqNum > expectedSeq) {
            // 有数据缺失，暂时停止重组
            break;
        }
        // 如果 segment->seqNum < expectedSeq，说明是重复数据，跳过
    }
    
    return result;
}

void TCPSessionManager::updateSessionState(TCPSession* session, uint16_t flags, bool isClientToServer) {
    // 简化的TCP状态机
    if (flags & TH_SYN) {
        if (session->state == TCP_CLOSED) {
            session->state = TCP_SYN_SENT;
        } else if (session->state == TCP_SYN_SENT) {
            session->state = TCP_ESTABLISHED;
        }
    } else if (flags & TH_FIN) {
        if (session->state == TCP_ESTABLISHED) {
            session->state = TCP_FIN_WAIT1;
        }
    } else if (flags & TH_RST) {
        session->state = TCP_CLOSED;
    }
}

bool TCPSessionManager::isClientToServer(const TCPConnection& sessionConn, const TCPConnection& packetConn) {
    // 如果数据包连接与会话连接完全匹配，则是客户端到服务器
    return (sessionConn.srcIP == packetConn.srcIP && 
            sessionConn.srcPort == packetConn.srcPort &&
            sessionConn.dstIP == packetConn.dstIP && 
            sessionConn.dstPort == packetConn.dstPort);
}

vector<pair<TCPConnection, string>> TCPSessionManager::getReassembledStreams() {
    vector<pair<TCPConnection, string>> result = m_reassembledStreams;
    m_reassembledStreams.clear();  // 清空已返回的数据
    return result;
}

void TCPSessionManager::cleanupExpiredSessions(int timeoutSeconds) {
    int currentTime = (int)time(nullptr);
    auto it = m_sessions.begin();
    
    while (it != m_sessions.end()) {
        if (currentTime - it->second->lastActivity > timeoutSeconds) {
            // printf("TCPSessionManager: 清理过期会话 %s:%d -> %s:%d\n",
            //        it->first.srcIP.c_str(), it->first.srcPort,
            //        it->first.dstIP.c_str(), it->first.dstPort);
            delete it->second;
            it = m_sessions.erase(it);
        } else {
            ++it;
        }
    }
}

TCPSessionManager::SessionStats TCPSessionManager::getStats() const {
    SessionStats stats;
    stats.totalSessions = m_sessions.size();
    stats.activeSessions = 0;
    stats.reassembledStreams = m_reassembledStreams.size();
    
    for (const auto& pair : m_sessions) {
        if (pair.second->state == TCP_ESTABLISHED) {
            stats.activeSessions++;
        }
    }
    
    return stats;
}
