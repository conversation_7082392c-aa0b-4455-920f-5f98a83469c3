//
//  TCPSessionManager.h
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef TCPSessionManager_h
#define TCPSessionManager_h

#include "common/common.h"
#include "common/msg.h"
#include <map>
#include <vector>
#include <netinet/tcp.h>
#include <netinet/ip.h>
#include <netinet/if_ether.h>
#include <arpa/inet.h>

using namespace std;

/**
 * TCP连接四元组，用于标识唯一的TCP会话
 */
struct TCPConnection {
    string srcIP;
    int srcPort;
    string dstIP;
    int dstPort;
    
    // 重载比较操作符，用于map的key
    bool operator<(const TCPConnection& other) const {
        if (srcIP != other.srcIP) return srcIP < other.srcIP;
        if (srcPort != other.srcPort) return srcPort < other.srcPort;
        if (dstIP != other.dstIP) return dstIP < other.dstIP;
        return dstPort < other.dstPort;
    }
    
    // 获取反向连接（用于双向会话）
    TCPConnection reverse() const {
        return {dstIP, dstPort, srcIP, srcPort};
    }
};

/**
 * TCP数据段，包含序列号和数据
 */
struct TCPSegment {
    uint32_t seqNum;        // 序列号
    uint32_t ackNum;        // 确认号
    uint16_t flags;         // TCP标志位
    size_t dataSize;        // 数据大小
    void* data;             // 数据内容
    int timestamp;          // 时间戳
    
    TCPSegment(uint32_t seq, uint32_t ack, uint16_t f, size_t size, void* d, int ts)
        : seqNum(seq), ackNum(ack), flags(f), dataSize(size), timestamp(ts) {
        if (size > 0 && d) {
            data = malloc(size);
            memcpy(data, d, size);
        } else {
            data = nullptr;
        }
    }
    
    ~TCPSegment() {
        if (data) {
            free(data);
        }
    }
};

/**
 * TCP会话状态
 */
enum TCPSessionState {
    TCP_CLOSED = 0,
    TCP_SYN_SENT,
    TCP_SYN_RECEIVED,
    TCP_ESTABLISHED,
    TCP_FIN_WAIT1,
    TCP_FIN_WAIT2,
    TCP_CLOSE_WAIT,
    TCP_CLOSING,
    TCP_LAST_ACK,
    TCP_TIME_WAIT
};

/**
 * TCP会话信息
 */
struct TCPSession {
    TCPConnection connection;
    TCPSessionState state;
    uint32_t clientSeq;     // 客户端序列号
    uint32_t serverSeq;     // 服务器序列号
    vector<TCPSegment*> clientSegments;  // 客户端数据段
    vector<TCPSegment*> serverSegments;  // 服务器数据段
    int lastActivity;       // 最后活动时间
    
    TCPSession(const TCPConnection& conn) 
        : connection(conn), state(TCP_CLOSED), clientSeq(0), serverSeq(0), lastActivity(0) {}
    
    ~TCPSession() {
        // 清理数据段
        for (auto seg : clientSegments) {
            delete seg;
        }
        for (auto seg : serverSegments) {
            delete seg;
        }
    }
};

/**
 * TCP会话管理器
 * 负责管理TCP连接、重组数据流、跟踪会话状态
 */
class TCPSessionManager {
public:
    TCPSessionManager();
    ~TCPSessionManager();
    
    // 处理网络数据包
    bool processPacket(const NetworkPacketData* packet);
    
    // 获取重组后的数据流
    vector<pair<TCPConnection, string>> getReassembledStreams();
    
    // 清理过期会话
    void cleanupExpiredSessions(int timeoutSeconds = 300);
    
    // 获取会话统计信息
    struct SessionStats {
        int totalSessions;
        int activeSessions;
        int reassembledStreams;
    };
    SessionStats getStats() const;

private:
    map<TCPConnection, TCPSession*> m_sessions;  // TCP会话映射
    vector<pair<TCPConnection, string>> m_reassembledStreams;  // 重组后的数据流
    
    // 解析TCP头部
    bool parseTCPHeader(const void* data, size_t dataSize, 
                       uint32_t& seqNum, uint32_t& ackNum, uint16_t& flags,
                       size_t& headerLen, const void*& payload, size_t& payloadSize);
    
    // 创建或获取TCP会话
    TCPSession* getOrCreateSession(const TCPConnection& conn);
    
    // 处理TCP数据段
    void processTCPSegment(TCPSession* session, const TCPSegment* segment, bool isClientToServer);
    
    // 重组数据流
    string reassembleStream(const vector<TCPSegment*>& segments);
    
    // 更新会话状态
    void updateSessionState(TCPSession* session, uint16_t flags, bool isClientToServer);
    
    // 判断是否为客户端到服务器的方向
    bool isClientToServer(const TCPConnection& conn, const TCPConnection& packetConn);
};

#endif /* TCPSessionManager_h */
