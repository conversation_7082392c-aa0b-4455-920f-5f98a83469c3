//
//  CParserPlugin.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/7.
//

#ifndef CParserPlugin_hpp
#define CParserPlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CParserTasker.h"
#include <memory>

class CParserPlugin : public IPlugin
{
public:
    CParserPlugin();
    virtual ~CParserPlugin();
    virtual PluginInfo* info() override;
    // handle方法已移除 - Tasker会直接将消息传递给下游的队列
    // control 和 telemetry 现在有默认实现，不需要重写
    virtual void init() override;
    virtual void uninit() override {}
    // start() 和 stop() 方法使用父类 IPlugin 的默认实现

    // 已删除：processMessage 方法不再需要

private:
    // 移除 m_thread_id 和 m_isRunning，由 IPlugin 统一管理
};

#endif /* DemoParser_hpp */