//
//  CHandler.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef C_Dispatcher_Tasker_h
#define C_Dispatcher_Tasker_h

#include "common/common.h"
#include "common/msg.h"
#include "common/interactorMsg.h"
#include "utils/utils.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"

using namespace std;

class CDispatcherTasker : public ITasker
{
public:
    CDispatcherTasker(IPlugin *plugin, map<int, list<IPlugin*>> plugins);
    virtual ~CDispatcherTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;
public:
    map<int, list<IPlugin*>> m_plugins;
};

#endif /* C_Dispatcher_Tasker_h */