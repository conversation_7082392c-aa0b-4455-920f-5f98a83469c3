//
//  CDispatcherHandler.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CDispatcherTasker.h"
#include "CDispatcherPlugin.h"

CDispatcherTasker::CDispatcherTasker(IPlugin *plugin, map<int, list<IPlugin*>> plugins): m_plugins(plugins)
{
    printf("new CDispatcherTasker \n");
    (void)plugin;  // 避免未使用参数警告
}

CDispatcherTasker::~CDispatcherTasker()
{
    printf("delete CDispatcherTasker  \n");
}

void* CDispatcherTasker::execute(void *msg)
{
    if(!msg)
    {
        usleep(1000000);
        return nullptr;
    }
    BaseControlMsg *m = (BaseControlMsg*)msg;
    sleep(2);
    switch(m->msgID)
    {
        case 201:
        {
            list<IPlugin*>::iterator iter = m_plugins[201].begin();
            for(;iter != m_plugins[201].end(); iter++)
            {
                // 直接将消息放入插件的队列
                Queue* pluginQueue = (*iter)->getQueue();
                if (pluginQueue) {
                    pluginQueue->produce(msg);
                }
            }
            break;
        }
        case 301:
        {
            list<IPlugin*>::iterator iter = m_plugins[301].begin();
            for(;iter != m_plugins[301].end(); iter++)
            {
                // 使用新的control接口
                (*iter)->control(301, msg);
            }

            delete (SourceInteractionMsg*)m;
            break;
        }
    }
    return nullptr;
}

void CDispatcherTasker::clear()
{
    return;
}