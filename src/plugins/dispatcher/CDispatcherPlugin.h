//
//  CDispatcherPlugin.hpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#ifndef CDispatcherPlugin_hpp
#define CDispatcherPlugin_hpp

#include "common/common.h"
#include "common/msg.h"
#include "core/IPlugin.h"
#include "CDispatcherTasker.h"
#include <memory>

class CDispatcherPlugin : public IPlugin
{
public:
    CDispatcherPlugin();
    virtual ~CDispatcherPlugin();
    virtual PluginInfo* info() override;

    // handle方法已移除 - Tasker会直接将消息传递给下游的队列

    // telemetry 现在有默认实现，不需要重写

    virtual void init() override;
    virtual void uninit() override {}
    virtual void start() override;
    virtual void stop() override { IPlugin::stop(); }
    void init_plugins(map<int, list<IPlugin*>> plugins){m_plugins = plugins;}

    // 设置插件映射，用于消息总线功能
    void set_plugins(const map<string, IPlugin*>& plugins) { m_plugin_map = plugins; }

public:
    map<int, list<IPlugin*>> m_plugins;
    map<string, IPlugin*> m_plugin_map;  // 插件映射表，用于消息总线
    
private:
};

#endif /* DemoDispatcher_hpp */