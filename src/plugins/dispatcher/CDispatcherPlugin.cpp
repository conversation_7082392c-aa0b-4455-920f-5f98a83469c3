//
//  CDispatcherPlugin.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/11/6.
//

#include "CDispatcherPlugin.h"

CDispatcherPlugin::CDispatcherPlugin()
{
    m_info = new PluginInfo("./config/dispatcher.json");
}

CDispatcherPlugin::~CDispatcherPlugin()
{
    printf("delete CPlugin \n");
    delete m_info;
}

PluginInfo* CDispatcherPlugin::info()
{
    return m_info;
}

void CDispatcherPlugin::init()
{
}

// handle方法已移除 - Tasker会直接将消息传递给下游的队列

void CDispatcherPlugin::start()
{
    // Dispatcher 插件不通过工作线程消费消息，其分发逻辑在 PluginManager 中处理
    // 因此，不创建 handlers 和启动工作线程
    // m_workers_size = 1;
    // for(int i = 0; i < m_workers_size; i++)
    // {
    //     IHandler *handler = new CDispatcherHandler(this, m_plugins);
    //     m_handlers.push_back(handler);
    // }
    // start_worker_threads(m_workers_size);

    return;
}