//
//  interactorMsg.h
//  pangoo
//
//  Created by <PERSON> on 2023/11/9.
//

#ifndef interactorMsg_h
#define interactorMsg_h

#include "common/common.h"
#include "common/msg.h"
#include <ctime>

using namespace std;

// 交互消息基类 - 继承控制消息基类
struct InteractionMsg : public BaseControlMsg
{
    string action;      // 交互动作
    // 移除 target 字段，因为是同步调用，调用时已明确目标
};

// 监控消息基类 - 继承控制消息基类
struct MonitorMsg : public BaseControlMsg
{
    string status;      // 监控状态
    string pluginName;  // 插件名称
};

// 紧急消息 - 继承控制消息基类
struct UrgentMsg : public BaseControlMsg
{
    int priority;       // 紧急级别
    string message;     // 紧急消息内容
};

// 源插件监控消息
struct SourceMonitorMsg : public MonitorMsg
{
    int messageCount;   // 处理的消息数量
    double cpuUsage;    // CPU使用率
    long memoryUsage;   // 内存使用量
};

// 解析器监控消息
struct ParserMonitorMsg : public MonitorMsg
{
    int processedCount; // 已处理数量
    int errorCount;     // 错误数量
    double avgProcessTime; // 平均处理时间
};

// 源插件交互消息
struct SourceInteractionMsg : public InteractionMsg
{
    string command;     // 控制命令 (start, stop, pause)
    string parameters;  // 命令参数
    // 不需要 target 字段，因为调用 source->control() 时已明确目标
};

// Web 输入命令消息 - 数据消息，不继承基类
struct WebInputMsg
{
    string command;     // 命令类型 (start, stop, restart, status)
    string target;      // 目标插件名称
    string params;      // 命令参数
    int timestamp;      // 时间戳
};

// 交互器状态消息 - 遥测消息，继承基类
struct InteractorStatusMsg : public BaseControlMsg
{
    string status;      // 状态 (running, stopped, error)
    string message;     // 状态描述
    int connections;    // 当前连接数
};

// 交互器控制消息 - 控制消息，继承基类
struct InteractorControlMsg : public BaseControlMsg
{
    string action;      // 动作 (enable, disable, configure)
    string config;      // 配置参数
    int priority;       // 优先级
};

// 交互器响应消息 - 数据消息，不继承基类
struct InteractorResponseMsg
{
    int requestId;      // 请求ID
    bool success;       // 是否成功
    string result;      // 结果数据
    string error;       // 错误信息
    int timestamp;      // 时间戳
};

// 遥测数据结构 - 遥测消息，继承基类
struct TelemetryData : public BaseControlMsg
{
    string pluginName;
    string status;
    int messageCount;
    double cpuUsage;
    long memoryUsage;
};

#endif /* interactorMsg_hpp */
