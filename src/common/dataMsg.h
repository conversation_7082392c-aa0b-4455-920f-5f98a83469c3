//
//  dataMsg.h
//  pangoo
//
//  Created by <PERSON> on 2023/11/9.
//

#ifndef dataMsg_h
#define dataMsg_h

#include "common/msg.h"

using namespace std;

// 数据流消息基类
struct DataMsg : public Msg
{
    // 继承基础字段，无需构造析构
};

// 源数据消息
struct SourceDataMsg : public DataMsg
{
    int dataValue;      // 数据值
    string dataType;    // 数据类型
    int timestamp;      // 时间戳
};

// 解析后数据消息
struct ParsedDataMsg : public DataMsg
{
    int originalValue;  // 原始值
    int parsedValue;    // 解析后的值
    string parseResult; // 解析结果
    int timestamp;      // 时间戳
};

// 上传数据消息
struct UploadDataMsg : public DataMsg
{
    int dataValue;      // 数据值
    string uploadPath;  // 上传路径
    string status;      // 上传状态
    int timestamp;      // 时间戳
};

#endif /* dataMsg_h */