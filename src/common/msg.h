//
//  msg.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/7.
//

#ifndef msg_h
#define msg_h

#include "common/common.h"
#include <set>
#include <map>
#include <mutex>

using namespace std;

// ========== 数据流消息（约定俗成的类型，不需要继承基类）==========

/**
 * 基础数据消息结构体 - 用于数据流传输
 * 约定俗成的类型，直接使用，不需要工厂创建
 */
struct MyMsg
{
    int m_i;
    int m_j;
    void *m_p;

    // 构造函数，方便创建
    MyMsg(int i = 0, int j = 0, void* p = nullptr) : m_i(i), m_j(j), m_p(p) {}
};

/**
 * 源到解析器的数据消息
 * 约定俗成的数据结构，队列ID=11
 */
struct SourceToParserData
{
    void* data;
    size_t dataSize;
    int timestamp;
    string dataType;

    // 构造函数
    SourceToParserData(void* d = nullptr, size_t size = 0, const string& type = "raw")
        : data(d), dataSize(size), timestamp((int)time(nullptr)), dataType(type) {}

    // 析构函数 - 释放数据内存
    ~SourceToParserData() {
        if (data) {
            free(data);
            data = nullptr;
        }
    }
};

/**
 * 解析器到上传器的数据消息
 * 约定俗成的数据结构，队列ID=21
 */
struct ParserToUploadData
{
    void* processedData;
    size_t dataSize;
    int timestamp;
    string format;
    bool isValid;

    // 构造函数
    ParserToUploadData(void* data = nullptr, size_t size = 0, const string& fmt = "json", bool valid = true)
        : processedData(data), dataSize(size), timestamp((int)time(nullptr)), format(fmt), isValid(valid) {}

    // 析构函数 - 释放处理后的数据内存
    ~ParserToUploadData() {
        if (processedData) {
            free(processedData);
            processedData = nullptr;
        }
    }
};

/**
 * 网络数据包消息结构
 * 用于传输从网卡捕获的数据包，队列ID=11
 */
struct NetworkPacketData
{
    // 数据包基本信息
    void* packetData;           // 原始数据包内容
    size_t packetSize;          // 数据包大小
    int timestamp;              // 捕获时间戳

    // 网络层信息
    string srcIP;               // 源IP地址
    string dstIP;               // 目标IP地址
    string protocol;            // 协议类型 (TCP/UDP/ICMP等)
    int srcPort;                // 源端口
    int dstPort;                // 目标端口

    // 数据链路层信息
    string srcMAC;              // 源MAC地址
    string dstMAC;              // 目标MAC地址
    string interface;           // 网卡接口名称

    // 构造函数
    NetworkPacketData(void* data = nullptr, size_t size = 0, const string& iface = "")
        : packetData(data), packetSize(size), timestamp((int)time(nullptr)),
          srcPort(0), dstPort(0), interface(iface), ownsData(true), isDestroyed(false) {}

    // 拷贝构造函数 - 禁用
    NetworkPacketData(const NetworkPacketData&) = delete;

    // 赋值操作符 - 禁用
    NetworkPacketData& operator=(const NetworkPacketData&) = delete;

    // 析构函数 - 释放数据包内存
    ~NetworkPacketData() {
        // 防止重复析构
        if (isDestroyed) {
            return;
        }
        isDestroyed = true;

        if (packetData && ownsData) {
            free(packetData);
            packetData = nullptr;
        }
        ownsData = false;
    }

    // 转移数据所有权
    void* releaseData() {
        void* data = packetData;
        packetData = nullptr;
        ownsData = false;
        return data;
    }

private:
    bool ownsData;      // 是否拥有数据的所有权
    bool isDestroyed;   // 是否已经析构过
};

/**
 * HTTP解析结果消息结构
 * 用于传输HTTP协议解析结果，队列ID=22
 */
struct HTTPParseResult
{
    // 连接信息
    string srcIP;
    string dstIP;
    int srcPort;
    int dstPort;
    int timestamp;

    // HTTP消息类型 (0=请求, 1=响应)
    int messageType;

    // HTTP请求信息
    string method;          // HTTP方法 (GET, POST等)
    string url;             // 完整URL
    string path;            // URL路径部分
    string query;           // URL查询参数
    string httpVersion;     // HTTP版本

    // HTTP响应信息
    int statusCode;         // HTTP状态码
    string statusMessage;   // 状态消息

    // HTTP头部信息 (JSON格式字符串)
    string headers;

    // HTTP消息体
    string body;
    size_t contentLength;

    // 解析元数据
    bool isComplete;        // 消息是否完整
    bool isValid;           // 解析是否成功

    // 构造函数
    HTTPParseResult()
        : srcPort(0), dstPort(0), timestamp((int)time(nullptr)), messageType(0),
          statusCode(0), contentLength(0), isComplete(false), isValid(false) {}
};

// ========== 控制和日志消息基类 ==========

// 控制和日志消息基类 - 包含msgID
struct BaseControlMsg
{
    int msgID;          // 消息ID
    int timestamp;      // 时间戳
    string source;      // 消息来源
};

// ========== 消息ID定义 ==========
enum DataMsgID {
    // 数据流消息（不使用msgID，直接传输数据）
    MSG_DATA_SOURCE_TO_PARSER = 11,
    MSG_DATA_PARSER_TO_UPLOAD = 21,
};

// 数据流消息ID映射表
static const std::map<uint32_t, std::string> DataMsgMap = {
    {MSG_DATA_SOURCE_TO_PARSER, "数据从源到解析器"},
    {MSG_DATA_PARSER_TO_UPLOAD, "数据从解析器到上传器"}
};

enum ControlMsgID {
    // 控制流消息（继承BaseControlMsg）
    MSG_CONTROL_SOURCE = 301,
    MSG_CONTROL_PARSER = 302,
    MSG_CONTROL_UPLOAD = 303,
};

enum TelemetryMsgID {
    // 遥测消息（继承BaseControlMsg）
    MSG_TELEMETRY_SOURCE = 201,
    MSG_TELEMETRY_PARSER = 202,
    MSG_TELEMETRY_UPLOAD = 203
};

// 消息ID和消息名映射表 - 用于消息管理器
struct MsgInfo
{
    int msgID;
    string msgName;
    string description;
};

#endif /* msg_h */
