#ifndef MESSAGE_FACTORY_H
#define MESSAGE_FACTORY_H

#include "msg.h"
#include "interactorMsg.h"
#include <string>
#include <memory>

using namespace std;

/**
 * 消息工厂类 - 负责创建控制流和日志流消息
 * 自动填充msgID，避免手动设置
 */
class MessageFactory
{
public:
    // ========== 控制流消息创建 ==========
    
    /**
     * 创建源插件控制消息
     */
    static SourceInteractionMsg* createSourceControlMsg(const string& action, const string& command, const string& parameters = "")
    {
        SourceInteractionMsg* msg = new SourceInteractionMsg();
        msg->msgID = MSG_CONTROL_SOURCE;
        msg->timestamp = (int)time(nullptr);
        msg->source = "interactor";
        msg->action = action;
        msg->command = command;
        msg->parameters = parameters;
        // 不设置 target，因为同步调用时已明确目标
        return msg;
    }
    
    /**
     * 创建解析器插件控制消息
     */
    static BaseControlMsg* createParserControlMsg(const string& action)
    {
        BaseControlMsg* msg = new BaseControlMsg();
        msg->msgID = MSG_CONTROL_PARSER;
        msg->timestamp = (int)time(nullptr);
        msg->source = "interactor";
        return msg;
    }
    
    /**
     * 创建上传器插件控制消息
     */
    static BaseControlMsg* createUploadControlMsg(const string& action)
    {
        BaseControlMsg* msg = new BaseControlMsg();
        msg->msgID = MSG_CONTROL_UPLOAD;
        msg->timestamp = (int)time(nullptr);
        msg->source = "interactor";
        return msg;
    }
    
    // ========== 遥测流消息创建 ==========
    
    /**
     * 创建源插件遥测消息
     */
    static SourceMonitorMsg* createSourceTelemetryMsg(int messageCount, double cpuUsage, long memoryUsage)
    {
        SourceMonitorMsg* msg = new SourceMonitorMsg();
        msg->msgID = MSG_TELEMETRY_SOURCE;
        msg->timestamp = (int)time(nullptr);
        msg->source = "source";
        msg->status = "running";
        msg->pluginName = "source";
        msg->messageCount = messageCount;
        msg->cpuUsage = cpuUsage;
        msg->memoryUsage = memoryUsage;
        return msg;
    }
    
    /**
     * 创建解析器插件遥测消息
     */
    static ParserMonitorMsg* createParserTelemetryMsg(int processedCount, int errorCount, double avgProcessTime)
    {
        ParserMonitorMsg* msg = new ParserMonitorMsg();
        msg->msgID = MSG_TELEMETRY_PARSER;
        msg->timestamp = (int)time(nullptr);
        msg->source = "parser";
        msg->status = "running";
        msg->pluginName = "parser";
        msg->processedCount = processedCount;
        msg->errorCount = errorCount;
        msg->avgProcessTime = avgProcessTime;
        return msg;
    }
    
    /**
     * 创建上传器插件遥测消息
     */
    static TelemetryData* createUploadTelemetryMsg(const string& pluginName, const string& status, int messageCount)
    {
        TelemetryData* msg = new TelemetryData();
        msg->msgID = MSG_TELEMETRY_UPLOAD;
        msg->timestamp = (int)time(nullptr);
        msg->source = "upload";
        msg->pluginName = pluginName;
        msg->status = status;
        msg->messageCount = messageCount;
        msg->cpuUsage = 0.0;
        msg->memoryUsage = 0;
        return msg;
    }
    
    /**
     * 创建通用遥测消息
     */
    static TelemetryData* createTelemetryMsg(const string& pluginName, const string& status, int messageCount, double cpuUsage, long memoryUsage)
    {
        TelemetryData* msg = new TelemetryData();
        // 根据插件名称自动确定msgID
        if (pluginName == "source") {
            msg->msgID = MSG_TELEMETRY_SOURCE;
        } else if (pluginName == "parser") {
            msg->msgID = MSG_TELEMETRY_PARSER;
        } else if (pluginName == "upload") {
            msg->msgID = MSG_TELEMETRY_UPLOAD;
        } else {
            msg->msgID = MSG_TELEMETRY_SOURCE; // 默认值
        }
        
        msg->timestamp = (int)time(nullptr);
        msg->source = pluginName;
        msg->pluginName = pluginName;
        msg->status = status;
        msg->messageCount = messageCount;
        msg->cpuUsage = cpuUsage;
        msg->memoryUsage = memoryUsage;
        return msg;
    }
    
    // ========== 交互流消息创建 ==========
    
    /**
     * 创建交互器状态消息
     */
    static InteractorStatusMsg* createInteractorStatusMsg(const string& status, const string& message, int connections)
    {
        InteractorStatusMsg* msg = new InteractorStatusMsg();
        msg->msgID = MSG_TELEMETRY_SOURCE; // 或者定义新的msgID
        msg->timestamp = (int)time(nullptr);
        msg->source = "interactor";
        msg->status = status;
        msg->message = message;
        msg->connections = connections;
        return msg;
    }
};

#endif // MESSAGE_FACTORY_H
