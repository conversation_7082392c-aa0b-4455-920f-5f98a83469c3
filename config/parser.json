{"module": "parser", "name": "parser", "dataflow": {"input": "11", "output": "21"}, "controlflow": [302], "workerNum": 3, "description": "HTTP协议解析器插件 - 进行TCP流重组和HTTP协议解析", "tcp_session": {"timeout_seconds": 300, "max_sessions": 1000, "cleanup_interval": 60}, "http_parser": {"max_header_size": 8192, "max_body_size": 1048576, "supported_methods": ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH"], "parse_body": true, "parse_query_params": true}, "output": {"format": "http_parse_result", "include_headers": true, "include_body": true, "max_body_output": 4096}}