{"files.associations": {"bitset": "cpp", "cstdio": "cpp", "cstring": "cpp", "fstream": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "new": "cpp", "sstream": "cpp", "streambuf": "cpp", "string": "cpp", "string_view": "cpp", "vector": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__split_buffer": "cpp", "__tree": "cpp", "__verbose_abort": "cpp", "any": "cpp", "array": "cpp", "cctype": "cpp", "charconv": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "complex": "cpp", "cstdarg": "cpp", "cstdint": "cpp", "cstdlib": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "execution": "cpp", "memory": "cpp", "forward_list": "cpp", "initializer_list": "cpp", "limits": "cpp", "mutex": "cpp", "optional": "cpp", "print": "cpp", "queue": "cpp", "ratio": "cpp", "span": "cpp", "stack": "cpp", "stdexcept": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "valarray": "cpp", "variant": "cpp", "algorithm": "cpp", "set": "cpp"}}