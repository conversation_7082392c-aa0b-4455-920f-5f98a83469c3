# Worker 架构设计文档

## 概述

为了更好地分离关注点和提高代码的可维护性，我们重构了插件架构，引入了 **CWorker** 类来专门处理线程管理和消息处理，将业务逻辑交给 **IHandler** 处理。

## 架构组件

### 1. CWorker - 工作线程管理器
**职责：**
- 管理工作线程的生命周期（启动/停止）
- 从队列中消费消息
- 将消息分发给 Handler 处理
- 提供线程安全的操作接口
- 统计处理信息

**主要接口：**
```cpp
class CWorker {
public:
    void start();                           // 启动工作线程
    void stop();                            // 停止工作线程
    void injectQueue(Queue* queue);         // 注入消息队列
    void addHandler(IHandler* handler);     // 添加业务处理器
    void setWorkerCount(int count);         // 设置工作线程数量
};
```

### 2. IHandler - 业务处理接口
**职责：**
- 实现具体的业务处理逻辑
- 处理消息转换和路由
- 与插件交互进行消息生产

**接口定义：**
```cpp
class IHandler {
public:
    virtual void execute(void *msg) = 0;    // 处理消息
    virtual void clear() = 0;               // 清理资源
};
```

### 3. IPlugin - 插件接口（简化）
**职责：**
- 提供插件配置和信息
- 管理 Workers（由 PluginManager 注入）
- 实现插件特定的业务接口
- 提供消息生产接口

**主要变化：**
- 移除了线程管理相关代码
- 添加了 Worker 管理接口
- 保留了队列注入和消息生产功能

### 4. PluginManager - 插件管理器（增强）
**职责：**
- 创建和管理所有组件（Plugin、Worker、Handler、Queue）
- 执行依赖注入
- 协调组件的生命周期

**新增功能：**
- `createWorkersForPlugin()` - 为插件创建 Workers
- `createHandlersForPlugin()` - 为插件创建 Handlers

## 对象关系图

```
PluginManager
├── 创建和管理 Plugins
├── 创建和管理 Queues
├── 创建和管理 Workers
└── 创建和管理 Handlers

Plugin
├── 包含多个 Workers
├── 引用 Consumer Queue
├── 引用 Producer Queues
└── 提供业务接口

Worker
├── 引用一个 Queue（消费）
├── 包含多个 Handlers
├── 管理多个工作线程
└── 负责消息分发

Handler
├── 引用关联的 Plugin
├── 实现业务处理逻辑
└── 通过 Plugin 生产消息
```

## 工作流程

1. **初始化阶段**
   ```
   PluginManager::init()
   ├── loadPlugins()           // 加载插件
   ├── initMsgQueue()          // 创建消息队列
   ├── initPlugins()           // 初始化插件
   └── initDI()                // 依赖注入
       ├── 注入队列到插件
       ├── createWorkersForPlugin()
       │   ├── 创建 Worker
       │   ├── 注入队列到 Worker
       │   ├── createHandlersForPlugin()
       │   └── 将 Worker 添加到插件
       └── 重复处理所有插件
   ```

2. **运行阶段**
   ```
   PluginManager::start()
   ├── 启动所有插件
   └── 启动所有 Workers
       └── Worker 启动工作线程
           └── 循环处理消息
               ├── 从队列消费消息
               ├── 分发给 Handler
               └── Handler 处理并生产结果
   ```

3. **停止阶段**
   ```
   PluginManager::stop()
   ├── 停止所有 Workers
   └── 停止所有插件
   ```

## 使用示例

### 基本使用
```cpp
// 1. 创建组件
CExamplePlugin* plugin = new CExamplePlugin();
CWorker* worker = new CWorker("ExampleWorker");
CExampleHandler* handler = new CExampleHandler(plugin);
Queue* queue = new Queue();

// 2. 配置关系
worker->injectQueue(queue);
worker->addHandler(handler);
plugin->addWorker(worker);
plugin->injectConsumerQueue(queue);

// 3. 启动系统
plugin->init();
plugin->start();
plugin->startWorkers();

// 4. 发送消息
queue->produce(message);

// 5. 停止系统
plugin->stopWorkers();
plugin->stop();
```

### 使用 PluginManager
```cpp
// 使用扩展的 PluginManager 自动管理所有组件
CExtendedPluginManager* manager = new CExtendedPluginManager();
manager->init();  // 自动创建所有组件和依赖关系
manager->start(); // 启动所有插件和 Workers
```

## 优势

1. **职责分离**：线程管理、消息处理、业务逻辑分别由不同类负责
2. **可扩展性**：易于添加新的 Handler 类型和处理逻辑
3. **可测试性**：各组件可以独立测试
4. **可维护性**：代码结构清晰，易于理解和修改
5. **线程安全**：Worker 提供线程安全的消息处理机制
6. **资源管理**：PluginManager 统一管理所有组件的生命周期

## 迁移指南

### 从旧架构迁移到新架构

1. **插件类修改**
   - 移除线程相关代码（`worker_thread_func`、`start_worker_threads` 等）
   - 移除 `m_worker_threads`、`m_handlers` 等成员变量
   - 添加 `m_workers` 成员变量
   - 实现简化的 `start()`、`stop()` 方法

2. **创建 Handler 类**
   - 继承 `IHandler` 接口
   - 将原来在插件中的消息处理逻辑移到 `execute()` 方法
   - 通过构造函数接收关联的插件引用

3. **更新 PluginManager**
   - 重写 `createHandlersForPlugin()` 方法
   - 根据插件类型创建对应的 Handler

### 配置文件无需修改
现有的插件配置文件（JSON）无需修改，新架构完全兼容现有配置格式。

## 最佳实践

1. **Handler 设计**
   - 保持 Handler 的无状态性，状态应该存储在插件中
   - Handler 应该专注于单一职责
   - 通过插件引用访问共享资源

2. **Worker 配置**
   - 根据业务需求合理设置工作线程数量
   - 考虑 CPU 核心数和 I/O 密集程度

3. **错误处理**
   - 在 Handler 中添加适当的异常处理
   - 记录处理失败的消息用于调试

4. **性能监控**
   - 利用 Worker 提供的统计信息监控系统性能
   - 定期检查消息处理速度和失败率
