# 场景视图

场景视图使用用例图描述系统的功能场景和用户交互，展示了不同角色如何使用系统完成各种任务。通过场景视图可以清晰地理解系统的业务价值和使用场景。

## 系统角色定义

### 主要角色
- **系统管理员 (Admin)**: 负责系统的安装、配置、启停等管理工作
- **系统操作员 (Operator)**: 负责日常的插件操作和系统监控
- **监控人员 (Monitor)**: 专注于系统性能监控、告警处理和数据分析
- **开发人员 (Developer)**: 负责插件开发、系统调试和问题排查

### 外部系统角色
- **数据源系统 (DataSource)**: 提供原始数据的外部系统
- **目标系统 (TargetSystem)**: 接收处理后数据的目标系统
- **第三方监控系统 (ExternalMonitor)**: 外部监控和告警系统

## 系统总体用例概览

### 核心用例总览图

```mermaid
graph TB
    %% 参与者定义
    Admin[系统管理员]
    Operator[系统操作员]
    Monitor[监控人员]
    DataSource[数据源系统]
    TargetSystem[目标系统]

    %% 核心用例定义
    subgraph "Pangoo v2.0 核心用例"
        UC1[启动/关闭系统]
        UC2[数据处理流水线]
        UC3[插件控制管理]
        UC4[系统监控告警]
        UC5[Web界面交互]
    end

    %% 参与者与用例的关系
    Admin --> UC1
    Admin --> UC3

    Operator --> UC3
    Operator --> UC5

    Monitor --> UC4
    Monitor --> UC5

    DataSource --> UC2
    UC2 --> TargetSystem

    %% 用例之间的关系
    UC1 -.->|enable| UC2
    UC1 -.->|enable| UC3
    UC2 -.->|monitored by| UC4
    UC3 -.->|monitored by| UC4
    UC4 -.->|displayed in| UC5

    %% 样式定义
    style UC1 fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    style UC2 fill:#f3e5f5,stroke:#4a148c,stroke-width:3px
    style UC3 fill:#fff3e0,stroke:#e65100,stroke-width:3px
    style UC4 fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px
    style UC5 fill:#fce4ec,stroke:#880e4f,stroke-width:3px
```

## 分模块用例图

### 1. 系统管理用例图

```mermaid
graph TB
    %% 参与者
    Admin[系统管理员]

    %% 系统管理用例
    subgraph "系统管理用例"
        UC1[启动系统]
        UC2[关闭系统]
        UC3[配置插件]
        UC4[查看系统状态]
        UC21[系统备份]
        UC22[系统恢复]
    end

    %% 关系
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC21
    Admin --> UC22

    %% 用例间关系
    UC1 -.->|prerequisite| UC3
    UC2 -.->|safe shutdown| UC21
    UC22 -.->|restore to| UC1

    style UC1 fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style UC2 fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style UC3 fill:#f0f4c3,stroke:#827717,stroke-width:2px
```

### 2. 数据处理用例图

```mermaid
graph TB
    %% 参与者
    DataSource[数据源系统]
    TargetSystem[目标系统]

    %% 数据处理用例
    subgraph "数据处理用例"
        UC5[采集数据]
        UC6[解析数据]
        UC7[上传数据]
        UC8[监控数据流]
        UC23[数据验证]
        UC24[数据转换]
    end

    %% 关系
    DataSource --> UC5
    UC7 --> TargetSystem

    %% 数据流关系
    UC5 --> UC6
    UC6 --> UC7
    UC5 -.->|include| UC23
    UC6 -.->|include| UC24
    UC5 -.->|monitored by| UC8
    UC6 -.->|monitored by| UC8
    UC7 -.->|monitored by| UC8

    style UC5 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style UC6 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style UC7 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style UC8 fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
```

### 3. 控制管理用例图

```mermaid
graph TB
    %% 参与者
    Operator[系统操作员]

    %% 控制管理用例
    subgraph "控制管理用例"
        UC9[启动插件]
        UC10[停止插件]
        UC11[重启插件]
        UC12[配置插件参数]
        UC25[插件热更新]
        UC26[插件状态查询]
    end

    %% 关系
    Operator --> UC9
    Operator --> UC10
    Operator --> UC11
    Operator --> UC12
    Operator --> UC25
    Operator --> UC26

    %% 用例间关系
    UC11 -.->|combine| UC10
    UC11 -.->|combine| UC9
    UC25 -.->|extend| UC9
    UC26 -.->|support| UC9
    UC26 -.->|support| UC10

    style UC9 fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style UC10 fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style UC11 fill:#fff3e0,stroke:#e65100,stroke-width:2px
```

### 4. 监控告警用例图

```mermaid
graph TB
    %% 参与者
    Monitor[监控人员]
    ExternalMonitor[第三方监控系统]

    %% 监控告警用例
    subgraph "监控告警用例"
        UC13[收集遥测数据]
        UC14[生成监控报告]
        UC15[处理系统告警]
        UC16[查看性能指标]
        UC27[设置告警阈值]
        UC28[告警通知]
    end

    %% 关系
    Monitor --> UC13
    Monitor --> UC14
    Monitor --> UC15
    Monitor --> UC16
    Monitor --> UC27
    UC28 --> ExternalMonitor

    %% 用例间关系
    UC13 -.->|generate| UC14
    UC13 -.->|trigger| UC15
    UC16 -.->|based on| UC27
    UC15 -.->|send| UC28

    style UC13 fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style UC15 fill:#ffebee,stroke:#c62828,stroke-width:2px
    style UC28 fill:#ffebee,stroke:#c62828,stroke-width:2px
```

### 5. Web交互用例图

```mermaid
graph TB
    %% 参与者
    Operator[系统操作员]
    Monitor[监控人员]

    %% Web交互用例
    subgraph "Web交互用例"
        UC17[Web界面操作]
        UC18[实时状态显示]
        UC19[历史数据查询]
        UC20[系统日志查看]
        UC29[用户权限管理]
        UC30[界面配置]
    end

    %% 关系
    Operator --> UC17
    Operator --> UC18
    Operator --> UC30
    Monitor --> UC18
    Monitor --> UC19
    Monitor --> UC20

    %% 用例间关系
    UC17 -.->|include| UC18
    UC17 -.->|require| UC29
    UC18 -.->|customized by| UC30

    style UC17 fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style UC18 fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style UC29 fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
```

## 业务场景流程图

### 典型数据处理场景

```mermaid
sequenceDiagram
    participant DS as 数据源系统
    participant SP as SourcePlugin
    participant PP as ParserPlugin
    participant UP as UploadPlugin
    participant TS as 目标系统
    participant MP as MonitorPlugin
    participant WEB as Web界面

    Note over DS, WEB: 典型数据处理业务场景

    DS->>SP: 发送原始数据
    Note right of DS: 外部数据源推送数据

    SP->>SP: 数据预处理
    SP->>PP: 发送到解析队列
    Note right of SP: 数据采集和初步处理

    PP->>PP: 数据解析和验证
    PP->>UP: 发送到上传队列
    Note right of PP: 格式转换和数据验证

    UP->>TS: 上传处理后数据
    TS->>UP: 确认接收
    Note right of UP: 数据上传和确认

    par 并行监控
        MP->>SP: 收集遥测数据
        SP->>MP: 返回状态信息
    and
        MP->>PP: 收集遥测数据
        PP->>MP: 返回状态信息
    and
        MP->>UP: 收集遥测数据
        UP->>MP: 返回状态信息
    end

    MP->>WEB: 更新监控界面
    Note right of MP: 实时状态更新

    Note over DS, WEB: 场景特点：异步处理，实时监控，可靠传输
```

### 系统启动场景

```mermaid
sequenceDiagram
    participant Admin as 系统管理员
    participant PM as PluginManager
    participant Config as 配置系统
    participant SP as SourcePlugin
    participant PP as ParserPlugin
    participant UP as UploadPlugin
    participant MP as MonitorPlugin
    participant WEB as Web服务

    Note over Admin, WEB: 系统启动业务场景

    Admin->>PM: 执行启动命令
    Note right of Admin: 管理员启动系统

    PM->>Config: 读取配置文件
    Config->>PM: 返回配置信息
    Note right of Config: 加载系统配置

    PM->>PM: 验证配置有效性
    Note right of PM: 配置验证和预处理

    par 并行初始化插件
        PM->>SP: 初始化Source插件
        SP->>PM: 初始化完成
    and
        PM->>PP: 初始化Parser插件
        PP->>PM: 初始化完成
    and
        PM->>UP: 初始化Upload插件
        UP->>PM: 初始化完成
    and
        PM->>MP: 初始化Monitor插件
        MP->>PM: 初始化完成
    end

    PM->>PM: 建立插件间连接
    Note right of PM: 消息队列和依赖注入

    par 并行启动插件
        PM->>SP: 启动插件线程
        SP->>PM: 启动成功
    and
        PM->>PP: 启动插件线程
        PP->>PM: 启动成功
    and
        PM->>UP: 启动插件线程
        UP->>PM: 启动成功
    and
        PM->>MP: 启动插件线程
        MP->>PM: 启动成功
    end

    PM->>WEB: 启动Web服务
    WEB->>PM: Web服务就绪

    PM->>Admin: 系统启动完成
    Note right of PM: 所有组件就绪

    Note over Admin, WEB: 启动特点：配置驱动，并行初始化，依赖管理
```

## 用例详细描述

### 1. 系统管理用例组

#### UC1: 启动系统
- **参与者**: 系统管理员
- **前置条件**: 系统已安装，配置文件存在且有效
- **主要流程**:
  1. 管理员执行启动命令 `./pangoo start`
  2. 系统读取并验证配置文件
  3. 按依赖关系加载所有插件模块
  4. 初始化消息队列和插件实例
  5. 建立插件间的连接关系
  6. 并行启动所有插件线程
  7. 启动Web服务和监控服务
  8. 系统进入运行状态
- **后置条件**: 系统正常运行，所有插件启动成功，Web界面可访问
- **异常流程**:
  - 配置文件不存在或格式错误：显示详细错误信息并退出
  - 插件加载失败：记录错误日志，跳过该插件继续启动
  - 端口被占用：尝试使用备用端口或提示用户修改配置

#### UC2: 关闭系统
- **参与者**: 系统管理员
- **前置条件**: 系统正在运行
- **主要流程**:
  1. 管理员发送关闭信号 (Ctrl+C 或 `./pangoo stop`)
  2. 系统设置停止标志，停止接收新数据
  3. 等待当前处理中的数据完成
  4. 按依赖关系逆序停止插件
  5. 保存运行状态和统计信息
  6. 释放系统资源（内存、文件句柄、网络连接）
  7. 系统安全退出
- **后置条件**: 系统完全关闭，资源释放完毕，无僵尸进程
- **异常流程**:
  - 强制关闭：记录未完成的任务，保存现场信息
  - 插件无响应：设置超时强制终止，记录异常状态

#### UC3: 配置插件
- **参与者**: 系统管理员
- **前置条件**: 系统已安装，具有配置文件写权限
- **主要流程**:
  1. 管理员编辑配置文件 `config/plugins.json`
  2. 系统验证配置参数的语法和语义
  3. 检查插件依赖关系的完整性
  4. 备份原配置文件
  5. 保存新配置
  6. 如果系统运行中，热重载配置
- **后置条件**: 插件配置更新成功，系统使用新配置运行
- **异常流程**:
  - 配置语法错误：显示具体错误位置和修改建议
  - 依赖关系冲突：提示冲突的插件和解决方案
  - 热重载失败：恢复原配置，建议重启系统

## 异常处理场景

### 插件故障恢复场景

```mermaid
sequenceDiagram
    participant MP as MonitorPlugin
    participant PM as PluginManager
    participant FP as 故障插件
    participant BP as 备用插件
    participant WEB as Web界面
    participant Admin as 管理员

    Note over MP, Admin: 插件故障自动恢复场景

    MP->>FP: 定期健康检查
    FP-->>MP: 无响应/异常
    Note right of FP: 插件发生故障

    MP->>MP: 检测到插件故障
    MP->>PM: 报告插件故障
    Note right of MP: 故障检测和报告

    PM->>FP: 尝试重启插件
    alt 重启成功
        FP->>PM: 重启成功
        PM->>MP: 插件恢复正常
        MP->>WEB: 更新状态为正常
    else 重启失败
        PM->>BP: 启动备用插件
        BP->>PM: 备用插件启动成功
        PM->>MP: 切换到备用插件
        MP->>WEB: 更新状态，显示使用备用插件
        MP->>Admin: 发送故障告警
        Note right of Admin: 通知管理员处理
    end

    Note over MP, Admin: 恢复特点：自动检测，快速切换，及时通知
```

### 系统过载处理场景

```mermaid
sequenceDiagram
    participant DS as 数据源
    participant SP as SourcePlugin
    participant Queue as 消息队列
    participant PP as ParserPlugin
    participant MP as MonitorPlugin
    participant PM as PluginManager

    Note over DS, PM: 系统过载处理场景

    DS->>SP: 大量数据涌入
    SP->>Queue: 数据入队
    Note right of SP: 数据量超过处理能力

    MP->>Queue: 监控队列长度
    Queue->>MP: 队列长度超过阈值
    Note right of MP: 检测到系统过载

    MP->>PM: 触发过载保护
    Note right of MP: 启动过载保护机制

    PM->>SP: 启用流量控制
    SP->>DS: 请求降低发送频率
    Note right of SP: 反压机制

    PM->>PP: 动态扩容处理线程
    PP->>PM: 增加处理能力
    Note right of PP: 弹性扩容

    alt 过载缓解
        MP->>Queue: 队列长度恢复正常
        MP->>PM: 解除过载保护
        PM->>SP: 恢复正常流量
        PM->>PP: 恢复正常线程数
    else 过载持续
        PM->>SP: 启用数据丢弃策略
        SP->>SP: 丢弃低优先级数据
        MP->>Admin: 发送过载告警
    end

    Note over DS, PM: 过载特点：反压控制，弹性扩容，优雅降级
```

### 2. 数据处理用例组

#### UC5: 采集数据
- **参与者**: 数据源系统, SourcePlugin
- **前置条件**: 数据源插件已启动，网络连接正常
- **主要流程**:
  1. 建立与外部数据源的连接
  2. 按配置的频率获取数据
  3. 验证数据格式和完整性
  4. 创建标准数据消息
  5. 添加时间戳和元数据
  6. 发送到数据处理队列
- **后置条件**: 数据成功发送到处理队列，更新采集统计
- **异常流程**:
  - 数据源不可用：启用重试机制，记录错误日志
  - 网络中断：缓存数据，网络恢复后批量发送
  - 数据格式错误：记录错误样本，继续处理其他数据

#### UC6: 解析数据
- **参与者**: ParserPlugin
- **前置条件**: 数据队列中有待处理数据，解析规则已配置
- **主要流程**:
  1. 从队列获取原始数据消息
  2. 识别数据类型和格式
  3. 应用相应的解析规则
  4. 验证解析结果的完整性
  5. 转换为目标格式
  6. 添加处理标记和时间戳
  7. 发送到上传队列
- **后置条件**: 数据解析完成并发送到下一环节，更新解析统计
- **异常流程**:
  - 解析规则不匹配：使用默认规则或跳过该数据
  - 数据损坏：记录错误详情，丢弃损坏数据
  - 内存不足：启用流式处理模式

#### UC7: 上传数据
- **参与者**: UploadPlugin, 目标系统
- **前置条件**: 上传队列中有处理好的数据，目标系统可访问
- **主要流程**:
  1. 从队列获取处理后数据
  2. 建立与目标系统的安全连接
  3. 按目标系统协议格式化数据
  4. 分批上传数据
  5. 等待目标系统确认
  6. 更新上传统计和状态
- **后置条件**: 数据成功上传到目标系统，获得确认回执
- **异常流程**:
  - 目标系统不可用：数据缓存到本地，定期重试
  - 网络超时：调整批次大小，增加重试间隔
  - 认证失败：刷新认证令牌，重新建立连接

### 3. 控制管理用例组

#### UC9: 启动插件
- **参与者**: 系统操作员
- **前置条件**: 系统运行中，目标插件已停止
- **主要流程**:
  1. 操作员通过Web界面选择插件
  2. 点击启动按钮
  3. 系统验证操作权限
  4. 发送启动控制消息
  5. 插件执行启动流程
  6. 返回启动结果
- **后置条件**: 插件成功启动并开始工作
- **异常流程**: 启动失败时显示错误信息

#### UC17: Web界面操作
- **参与者**: 系统操作员
- **前置条件**: Web服务正常运行
- **主要流程**:
  1. 操作员访问Web界面
  2. 查看系统当前状态
  3. 选择要执行的操作
  4. 确认操作参数
  5. 提交操作请求
  6. 查看操作结果
- **后置条件**: 操作成功执行，界面更新状态
- **异常流程**: 操作失败时显示详细错误信息

### 4. 监控告警用例组

#### UC13: 收集遥测数据
- **参与者**: 监控人员, CMonitorPlugin
- **前置条件**: 系统正常运行
- **主要流程**:
  1. 定时器触发遥测收集
  2. 并行收集各插件遥测数据
  3. 汇总系统性能指标
  4. 检查告警阈值
  5. 更新监控缓存
- **后置条件**: 遥测数据收集完成并缓存
- **异常流程**: 插件无响应时记录异常状态

#### UC15: 处理系统告警
- **参与者**: 监控人员
- **前置条件**: 系统检测到异常情况
- **主要流程**:
  1. 系统触发告警
  2. 记录告警详细信息
  3. 通知相关人员
  4. 监控人员查看告警
  5. 分析告警原因
  6. 执行相应处理措施
- **后置条件**: 告警得到处理，系统恢复正常
- **异常流程**: 告警处理失败时升级告警级别

## 用例实现映射

### 插件与用例的对应关系

```mermaid
graph LR
    subgraph "插件实现"
        CSourcePlugin[CSourcePlugin]
        CParserPlugin[CParserPlugin]
        CUploadPlugin[CUploadPlugin]
        CInteractorPlugin[CInteractorPlugin]
        CMonitorPlugin[CMonitorPlugin]
        CDispatcherPlugin[CDispatcherPlugin]
    end

    subgraph "用例实现"
        UC5[生成数据]
        UC6[解析数据]
        UC7[上传数据]
        UC17[Web界面操作]
        UC13[收集遥测数据]
        UC9[控制管理]
    end

    CSourcePlugin --> UC5
    CParserPlugin --> UC6
    CUploadPlugin --> UC7
    CInteractorPlugin --> UC17
    CMonitorPlugin --> UC13
    CDispatcherPlugin --> UC9

    style CSourcePlugin fill:#f9f,stroke:#333,stroke-width:2px
    style CParserPlugin fill:#bbf,stroke:#333,stroke-width:2px
    style CUploadPlugin fill:#bfb,stroke:#333,stroke-width:2px
    style CInteractorPlugin fill:#ffb,stroke:#333,stroke-width:2px
    style CMonitorPlugin fill:#fbb,stroke:#333,stroke-width:2px
    style CDispatcherPlugin fill:#bff,stroke:#333,stroke-width:2px
```

## 性能场景

### 高并发数据处理场景

```mermaid
sequenceDiagram
    participant LB as 负载均衡器
    participant SP1 as SourcePlugin-1
    participant SP2 as SourcePlugin-2
    participant SP3 as SourcePlugin-3
    participant PP1 as ParserPlugin-1
    participant PP2 as ParserPlugin-2
    participant PP3 as ParserPlugin-3
    participant UP as UploadPlugin
    participant MP as MonitorPlugin

    Note over LB, MP: 高并发数据处理场景

    par 并行数据采集
        LB->>SP1: 分发数据源1
        LB->>SP2: 分发数据源2
        LB->>SP3: 分发数据源3
    end

    par 并行数据处理
        SP1->>PP1: 发送到解析队列1
        SP2->>PP2: 发送到解析队列2
        SP3->>PP3: 发送到解析队列3
    end

    par 并行解析处理
        PP1->>PP1: 解析数据1
        PP2->>PP2: 解析数据2
        PP3->>PP3: 解析数据3
    end

    par 汇聚上传
        PP1->>UP: 发送解析结果1
        PP2->>UP: 发送解析结果2
        PP3->>UP: 发送解析结果3
    end

    UP->>UP: 批量上传处理
    Note right of UP: 批量优化上传性能

    MP->>MP: 实时性能监控
    Note right of MP: 监控吞吐量和延迟

    Note over LB, MP: 性能特点：水平扩展，并行处理，批量优化
```

### 系统扩容场景

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant PM as PluginManager
    participant Config as 配置系统
    participant NewPlugin as 新插件实例
    participant ExistingPlugin as 现有插件
    participant MP as MonitorPlugin

    Note over Admin, MP: 动态扩容场景

    MP->>MP: 检测性能瓶颈
    MP->>Admin: 发送扩容建议
    Note right of MP: 基于性能指标建议扩容

    Admin->>Config: 修改插件实例配置
    Config->>PM: 通知配置变更
    Note right of Admin: 管理员决定扩容

    PM->>NewPlugin: 创建新插件实例
    NewPlugin->>PM: 实例创建成功
    Note right of PM: 动态创建插件实例

    PM->>NewPlugin: 初始化插件
    NewPlugin->>PM: 初始化完成

    PM->>PM: 重新分配负载
    Note right of PM: 调整负载分配策略

    PM->>ExistingPlugin: 更新路由配置
    ExistingPlugin->>PM: 配置更新完成

    PM->>NewPlugin: 启动插件
    NewPlugin->>PM: 插件启动成功

    MP->>MP: 监控扩容效果
    MP->>Admin: 报告扩容结果
    Note right of MP: 验证扩容效果

    Note over Admin, MP: 扩容特点：动态扩容，负载重分配，效果验证
```

## 场景扩展性

### 未来可扩展的用例

#### 高级功能用例
- **UC35**: 插件热更新 - 支持运行时更新插件代码
- **UC36**: 分布式部署 - 支持多节点集群部署
- **UC37**: 数据备份恢复 - 自动数据备份和灾难恢复
- **UC38**: 智能路由 - 基于内容的智能数据路由
- **UC39**: 数据压缩 - 自动数据压缩和解压缩

#### 运维管理用例
- **UC40**: 性能调优 - 自动性能分析和优化建议
- **UC41**: 安全审计 - 系统安全事件审计和分析
- **UC42**: 容量规划 - 基于历史数据的容量预测
- **UC43**: 故障预测 - 基于机器学习的故障预测
- **UC44**: 自动化运维 - 自动化的运维操作和决策

#### 业务扩展用例
- **UC45**: 多租户支持 - 支持多租户隔离和管理
- **UC46**: 数据治理 - 数据质量管理和合规性检查
- **UC47**: 实时分析 - 流式数据实时分析和处理
- **UC48**: 机器学习集成 - 集成机器学习模型进行数据处理
- **UC49**: API网关 - 提供统一的API访问接口

### 用例优先级矩阵

| 优先级 | 核心功能 | 扩展功能 | 运维功能 |
|--------|----------|----------|----------|
| **高优先级** | UC1(启动系统)<br/>UC2(关闭系统)<br/>UC5(采集数据)<br/>UC6(解析数据)<br/>UC7(上传数据)<br/>UC13(收集遥测) | UC23(数据验证)<br/>UC24(数据转换) | UC15(处理告警)<br/>UC16(性能指标) |
| **中优先级** | UC9(启动插件)<br/>UC10(停止插件)<br/>UC17(Web操作)<br/>UC18(实时显示) | UC25(热更新)<br/>UC26(状态查询)<br/>UC27(告警阈值) | UC19(历史查询)<br/>UC20(日志查看)<br/>UC28(告警通知) |
| **低优先级** | UC3(配置插件)<br/>UC4(查看状态)<br/>UC12(配置参数) | UC29(权限管理)<br/>UC30(界面配置)<br/>UC35-UC49(扩展功能) | UC31(插件调试)<br/>UC32(性能分析)<br/>UC33(错误诊断)<br/>UC34(日志分析) |

### 场景演进路线图

#### 第一阶段 (v2.0) - 核心功能
- 实现基础的数据采集、解析、上传功能
- 提供基本的监控和Web界面
- 支持插件化架构和配置管理

#### 第二阶段 (v2.1) - 增强功能
- 添加插件热更新和动态扩容
- 增强监控和告警功能
- 提供更丰富的Web界面功能

#### 第三阶段 (v2.2) - 高级功能
- 支持分布式部署和集群管理
- 集成机器学习和智能分析
- 提供完整的运维自动化功能

#### 第四阶段 (v3.0) - 企业级功能
- 多租户和企业级安全
- 完整的数据治理和合规性
- 云原生和容器化部署

这种基于场景的视图设计更清晰地展示了系统的功能边界、用户交互方式和未来扩展方向，便于理解系统的业务价值和技术演进路径。


# 逻辑视图

```mermaid
classDiagram
    %% 核心架构层
    class PluginManager {
        <<singleton>>
        +getInstance() PluginManager
        +init() void
        +start() void
        +loadPlugins() void
        +sendMessage(msgID, data) void

        -plugins map
        -queues map
    }

    class IPlugin {
        <<interface>>
        +init() void
        +start() void
        +handle(msg) int
        +control(msgID, data) void
        +telemetry() TelemetryData

        #consumerQueue Queue
        #producerQueues map
        #handler IHandler
    }

    class IHandler {
        <<interface>>
        +process(msg) void
    }

    class Queue {
        +push(msg) void
        +pop() Msg
        +size() int
    }

    %% 数据流消息（不继承基类）
    class MyMsg {
        +m_i int
        +m_j int
        +m_p void*
    }

    class SourceToParserData {
        +data void*
        +dataSize size_t
        +timestamp int
        +dataType string
    }

    class ParserToUploadData {
        +processedData void*
        +dataSize size_t
        +timestamp int
        +format string
        +isValid bool
    }

    %% 控制和日志消息基类
    class BaseControlMsg {
        +msgID int
        +timestamp int
        +source string
    }

    %% 控制消息
    class InteractionMsg {
        +action string
    }

    class MonitorMsg {
        +status string
        +pluginName string
    }

    class TelemetryData {
        +pluginName string
        +status string
        +messageCount int
        +cpuUsage double
        +memoryUsage long
    }

    %% 系统插件层
    class InteractPlugin {
        +processWebInput() void
        +dispatchControl() void
    }

    class DispatcherPlugin {
        +dispatch(msgID, msg) void
        +registerHandler(msgID, plugin) void
    }

    class MonitorPlugin {
        +collectTelemetry() void
        +generateReport() void
    }

    %% 业务插件层
    class SourcePlugin {
        +generateData() void
        +sendToParser() void
    }

    class ParserPlugin {
        +parseData(data) void
        +sendToUpload() void
    }

    %% 关系设计
    PluginManager "1" *-- "*" IPlugin : manages
    IPlugin "1" *-- "1" IHandler : delegates to
    IPlugin "1" o-- "*" Queue : uses

    IPlugin <|-- InteractPlugin
    IPlugin <|-- DispatcherPlugin
    IPlugin <|-- MonitorPlugin
    IPlugin <|-- SourcePlugin
    IPlugin <|-- ParserPlugin

    SourcePlugin --> ParserPlugin : data flow
    InteractPlugin --> DispatcherPlugin : control flow
    MonitorPlugin --> IPlugin : telemetry flow

    %% 消息继承关系
    BaseControlMsg <|-- InteractionMsg
    BaseControlMsg <|-- MonitorMsg
    BaseControlMsg <|-- TelemetryData

    %% 数据流消息关系（不继承基类）
    Queue --> MyMsg : contains data
    Queue --> SourceToParserData : contains data
    Queue --> ParserToUploadData : contains data

    %% 控制流消息关系
    IHandler --> BaseControlMsg : processes control
    IHandler --> InteractionMsg : processes interaction
    IHandler --> MonitorMsg : processes monitor
    IHandler --> TelemetryData : processes telemetry
```

## 数据流

### 控制流(消息总线)
1. 通过DispatcherPlugin进行实现
2. 这个类包含所有消息的类型和其订阅的插件
3. 消息总线的触发来源是web端程序，通过管道通知交互插件
4. 交互插件处理管道的消息，然后通过dispatch进行分发(同步调用)
5. 而其他的业务类或者是数据流中的节点是不需要发出控制流的，而是被动接受的

```mermaid
sequenceDiagram
    participant Web as Web端程序
    participant Pipe as 管道
    participant Interactor as InteractPlugin
    participant Dispatcher as DispatcherPlugin
    participant Source as SourcePlugin
    participant Parser as ParserPlugin
    participant Upload as UploadPlugin
    participant Monitor as MonitorPlugin

    Note over Web, Monitor: 控制流 - 通过消息总线进行同步调用

    Web->>Pipe: 发送控制命令
    Note right of Web: 用户操作触发控制命令

    Pipe->>Interactor: 管道通知
    Note right of Pipe: 进程间通信

    Interactor->>Interactor: 处理管道消息
    Note right of Interactor: 解析控制命令

    Interactor->>Dispatcher: dispatch(controlMsg)
    Note right of Interactor: 通过分发器分发控制消息

    alt 控制Source插件
        Dispatcher->>Source: control(msg)
        Source->>Dispatcher: 返回执行结果
    else 控制Parser插件
        Dispatcher->>Parser: control(msg)
        Parser->>Dispatcher: 返回执行结果
    else 控制Upload插件
        Dispatcher->>Upload: control(msg)
        Upload->>Dispatcher: 返回执行结果
    else 控制Monitor插件
        Dispatcher->>Monitor: control(msg)
        Monitor->>Dispatcher: 返回执行结果
    end

    Dispatcher->>Interactor: 返回分发结果
    Interactor->>Pipe: 返回处理结果
    Pipe->>Web: 返回执行状态

    Note over Web, Monitor: 控制流特点：同步调用，实时响应
```

### 数据流(pipeline)
1. 每个插件会注册自己能处理的消息类型
2. 每个插件会注册一个队列，用于接受消息
3. 每个插件会创建一个线程，用于处理队列中的消息
4. 每个插件会创建一个worker，用于处理消息
5. 每个插件处理完消息后会将消息压入到消息队列，而不用管下游是谁

```mermaid
sequenceDiagram
    participant Source as SourcePlugin
    participant SQueue as Source队列
    participant SWorker as SourceWorker
    participant MsgQueue11 as 消息队列11
    participant Parser as ParserPlugin
    participant PQueue as Parser队列
    participant PWorker as ParserWorker
    participant MsgQueue21 as 消息队列21
    participant Upload as UploadPlugin
    participant UQueue as Upload队列
    participant UWorker as UploadWorker

    Note over Source, UWorker: 数据流 - Pipeline异步处理

    Source->>SQueue: 接收外部数据
    Note right of Source: 数据源接收原始数据

    SQueue->>SWorker: 队列通知
    SWorker->>SWorker: process(data)
    Note right of SWorker: 处理数据

    SWorker->>MsgQueue11: 发送处理后的数据
    Note right of SWorker: 发送到消息队列11<br/>不关心下游是谁

    MsgQueue11->>Parser: 队列分发
    Note right of MsgQueue11: 自动分发给注册的插件

    Parser->>PQueue: 接收数据
    PQueue->>PWorker: 队列通知
    PWorker->>PWorker: process(data)
    Note right of PWorker: 解析数据

    PWorker->>MsgQueue21: 发送解析后的数据
    Note right of PWorker: 发送到消息队列21<br/>不关心下游是谁

    MsgQueue21->>Upload: 队列分发
    Note right of MsgQueue21: 自动分发给注册的插件

    Upload->>UQueue: 接收数据
    UQueue->>UWorker: 队列通知
    UWorker->>UWorker: process(data)
    Note right of UWorker: 上传数据

    Note over Source, UWorker: 数据流特点：异步处理，队列缓存，插件解耦
```

### 日志流
1. 定义一个或多个日志消息
2. 所有的插件都按照这个格式生成日志消息
3. 日志插件每分钟轮询一次，获取所有插件的日志消息，然后进行处理

```mermaid
sequenceDiagram
    participant Timer as 定时器
    participant Monitor as MonitorPlugin
    participant Source as SourcePlugin
    participant Parser as ParserPlugin
    participant Upload as UploadPlugin
    participant Interactor as InteractPlugin
    participant Dispatcher as DispatcherPlugin
    participant LogProcessor as 日志处理器

    Note over Timer, LogProcessor: 日志流 - 定时轮询获取遥测数据

    Timer->>Monitor: 每分钟触发
    Note right of Timer: 定时任务触发

    Monitor->>Monitor: 开始收集遥测数据
    Note right of Monitor: 启动遥测收集流程

    par 并行收集各插件遥测数据
        Monitor->>Source: telemetry()
        Source->>Monitor: 返回遥测数据
        Note right of Source: 状态、消息数、性能指标
    and
        Monitor->>Parser: telemetry()
        Parser->>Monitor: 返回遥测数据
        Note right of Parser: 状态、消息数、性能指标
    and
        Monitor->>Upload: telemetry()
        Upload->>Monitor: 返回遥测数据
        Note right of Upload: 状态、消息数、性能指标
    and
        Monitor->>Interactor: telemetry()
        Interactor->>Monitor: 返回遥测数据
        Note right of Interactor: 状态、消息数、性能指标
    and
        Monitor->>Dispatcher: telemetry()
        Dispatcher->>Monitor: 返回遥测数据
        Note right of Dispatcher: 状态、消息数、性能指标
    and
        Monitor->>Monitor: telemetry()
        Note right of Monitor: 自身遥测数据
    end

    Monitor->>LogProcessor: 处理收集的遥测数据
    Note right of Monitor: 格式化、聚合、分析

    LogProcessor->>LogProcessor: 生成遥测报告
    Note right of LogProcessor: 存储、展示、告警

    Note over Timer, LogProcessor: 日志流特点：定时轮询，主动获取，批量处理
```

## DFD
1. 控制流+日志流都是同步的消息，需要消息ID或子类型，但是不需要依赖注入等方式，也就是说没有异步调用的场景
2. 数据流是异步的，不需要消息ID。但是需要有描述信息，描述消息的ID，描述，消息结构的名称


# 开发视图

## 插件生命周期管理

```mermaid
sequenceDiagram
    participant PM as PluginManager
    participant Plugin as IPlugin
    participant Queue as Queue
    participant Thread as Thread
    participant Worker as IHandler

    Note over PM, Worker: 插件生命周期管理

    PM->>PM: init()
    Note right of PM: 插件管理器初始化

    PM->>PM: loadPlugins()
    Note right of PM: 加载插件动态库

    PM->>Plugin: 创建插件实例
    Plugin->>PM: 返回插件实例

    PM->>PM: initMsgQueue()
    Note right of PM: 初始化消息队列

    PM->>Queue: 创建队列
    Queue->>PM: 返回队列实例

    PM->>Plugin: 注册消息类型
    Plugin->>PM: 确认注册

    PM->>PM: initPlugins()
    Note right of PM: 初始化所有插件

    PM->>Plugin: init()
    Plugin->>Worker: 创建Worker
    Worker->>Plugin: 返回Worker实例
    Plugin->>PM: 初始化完成

    PM->>PM: initDI()
    Note right of PM: 依赖注入

    PM->>Plugin: 注入队列
    Plugin->>PM: 确认注入

    PM->>PM: initBus()
    Note right of PM: 初始化消息总线

    PM->>PM: start()
    Note right of PM: 启动所有插件

    PM->>Plugin: start()
    Plugin->>Thread: 创建处理线程
    Thread->>Plugin: 线程启动成功
    Plugin->>PM: 启动完成

    Note over PM, Worker: 运行时处理

    Queue->>Plugin: 消息到达
    Plugin->>Thread: 通知处理线程
    Thread->>Worker: process(msg)
    Worker->>Thread: 处理完成
    Thread->>Queue: 发送结果消息

    Note over PM, Worker: 插件特点：独立线程，异步处理，队列通信
```

# 进程视图

## 插件实例化原则
1. **一个插件对象 = 一个队列** - 每个插件实例只拥有一个消费队列，确保单一数据源
2. **分流通过多实例实现** - 如需分流处理，根据配置创建多个插件实例
3. **独立下游保证** - 每个队列都有独立的下游消费者，避免MPMC竞争
4. **配置驱动创建** - 插件实例数量和连接关系由配置文件决定

## 进程架构设计
1. 总共分两个程序，一个主进程，还有一个是web进程
2. 进程间的通信使用管道的方式
3. 主程序间的流图参考逻辑视图

## 插件实例化序列图

```mermaid
sequenceDiagram
    participant Config as 配置文件
    participant PM as PluginManager
    participant Source1 as SourcePlugin-1
    participant Queue1 as Queue-1
    participant Parser1 as ParserPlugin-1
    participant Source2 as SourcePlugin-2
    participant Queue2 as Queue-2
    participant Parser2 as ParserPlugin-2

    Note over Config, Parser2: 插件实例化 - 一个插件一个队列原则

    PM->>Config: 读取插件配置
    Config->>PM: 返回插件实例配置
    Note right of Config: 配置指定需要创建的插件实例数量

    PM->>PM: 解析配置，确定实例数量
    Note right of PM: 根据分流需求创建多个实例

    par 创建第一组插件实例
        PM->>Source1: 创建 SourcePlugin 实例1
        Source1->>PM: 实例创建完成
        PM->>Queue1: 创建专属队列1
        Queue1->>PM: 队列创建完成
        PM->>Parser1: 创建 ParserPlugin 实例1
        Parser1->>PM: 实例创建完成
    and 创建第二组插件实例
        PM->>Source2: 创建 SourcePlugin 实例2
        Source2->>PM: 实例创建完成
        PM->>Queue2: 创建专属队列2
        Queue2->>PM: 队列创建完成
        PM->>Parser2: 创建 ParserPlugin 实例2
        Parser2->>PM: 实例创建完成
    end

    PM->>Source1: 注入专属队列1
    Source1->>PM: 队列注入完成
    PM->>Parser1: 注入专属队列1
    Parser1->>PM: 队列注入完成

    PM->>Source2: 注入专属队列2
    Source2->>PM: 队列注入完成
    PM->>Parser2: 注入专属队列2
    Parser2->>PM: 队列注入完成

    Note over Config, Parser2: 结果：每个插件实例拥有独立队列，避免MPMC竞争
```

## 队列分配策略图

```mermaid
graph TD
    subgraph "错误的MPMC模式"
        A1[Source-1] --> Q1[共享队列]
        A2[Source-2] --> Q1
        Q1 --> B1[Parser-1]
        Q1 --> B2[Parser-2]
        Q1 -.-> X[竞争条件]
    end

    subgraph "正确的SPSC模式 - 1对1"
        C1[Source-1] --> Q2[Queue-1]
        Q2 --> D1[Parser-1]

        C2[Source-2] --> Q3[Queue-2]
        Q3 --> D2[Parser-2]

        C3[Source-3] --> Q4[Queue-3]
        Q4 --> D3[Parser-3]
    end

    subgraph "正确的SPSC模式 - 1对多（扇出）"
        E1[Source-1] --> Q5[Queue-1-1]
        E1 --> Q6[Queue-1-2]
        E1 --> Q7[Queue-1-3]
        Q5 --> F1[Parser-1-1]
        Q6 --> F2[Parser-1-2]
        Q7 --> F3[Parser-1-3]

        E2[Source-2] --> Q8[Queue-2-1]
        E2 --> Q9[Queue-2-2]
        E2 --> Q10[Queue-2-3]
        Q8 --> F4[Parser-2-1]
        Q9 --> F5[Parser-2-2]
        Q10 --> F6[Parser-2-3]
    end

    subgraph "配置驱动的实例创建"
        Config[配置文件] --> PM[PluginManager]
        PM --> Instance1[插件实例1]
        PM --> Instance2[插件实例2]
        PM --> Instance3[插件实例3]
        Instance1 --> Queue1[专属队列1]
        Instance2 --> Queue2[专属队列2]
        Instance3 --> Queue3[专属队列3]
    end

    style Q1 fill:#ffcccc
    style X fill:#ff6666
    style Q2 fill:#ccffcc
    style Q3 fill:#ccffcc
    style Q4 fill:#ccffcc
    style Q5 fill:#ccffcc
    style Q6 fill:#ccffcc
    style Q7 fill:#ccffcc
    style Q8 fill:#ccffcc
    style Q9 fill:#ccffcc
    style Q10 fill:#ccffcc
```

## 插件配置示例

### 配置文件结构

#### 1对1模式配置
```json
{
  "plugins": [
    {
      "type": "source",
      "instances": [
        {"name": "source-data1", "config": {"input": "sensor1", "rate": 100}},
        {"name": "source-data2", "config": {"input": "sensor2", "rate": 200}}
      ]
    },
    {
      "type": "parser",
      "instances": [
        {"name": "parser-data1", "config": {"format": "json", "validation": true}},
        {"name": "parser-data2", "config": {"format": "xml", "validation": false}}
      ]
    }
  ],
  "connections": [
    {"from": "source-data1", "to": "parser-data1"},
    {"from": "source-data2", "to": "parser-data2"}
  ]
}
```

#### 1对多扇出模式配置
```json
{
  "plugins": [
    {
      "type": "source",
      "instances": [
        {"name": "source-1", "config": {"input": "sensor1", "rate": 300}},
        {"name": "source-2", "config": {"input": "sensor2", "rate": 300}}
      ]
    },
    {
      "type": "parser",
      "instances": [
        {"name": "parser-1-1", "config": {"format": "json", "thread": 1}},
        {"name": "parser-1-2", "config": {"format": "json", "thread": 2}},
        {"name": "parser-1-3", "config": {"format": "json", "thread": 3}},
        {"name": "parser-2-1", "config": {"format": "xml", "thread": 1}},
        {"name": "parser-2-2", "config": {"format": "xml", "thread": 2}},
        {"name": "parser-2-3", "config": {"format": "xml", "thread": 3}}
      ]
    }
  ],
  "connections": [
    {"from": "source-1", "to": ["parser-1-1", "parser-1-2", "parser-1-3"]},
    {"from": "source-2", "to": ["parser-2-1", "parser-2-2", "parser-2-3"]}
  ],
  "fanout_policy": "round_robin"
}
```

### 实例化结果

#### 1对1模式
- **source-data1** ↔ **queue-1** ↔ **parser-data1** (独立通道1)
- **source-data2** ↔ **queue-2** ↔ **parser-data2** (独立通道2)

#### 1对多扇出模式
- **source-1** → **queue-1-1** → **parser-1-1**
- **source-1** → **queue-1-2** → **parser-1-2**
- **source-1** → **queue-1-3** → **parser-1-3**
- **source-2** → **queue-2-1** → **parser-2-1**
- **source-2** → **queue-2-2** → **parser-2-2**
- **source-2** → **queue-2-3** → **parser-2-3**

#### 核心原则
- 每个通道都是 **SPSC** (Single Producer Single Consumer) 模式
- **扇出数量相等** - 每个上游插件的下游数量必须相等
- **负载均衡** - 上游插件将数据均匀分发到多个下游
- 避免了队列竞争和数据竞态条件

## 进程间通信序列图

```mermaid
sequenceDiagram
    participant WebProcess as Web进程
    participant Pipe as 管道
    participant MainProcess as 主进程
    participant PluginManager as PluginManager
    participant InteractPlugin as InteractPlugin

    Note over WebProcess, InteractPlugin: 进程间通信 - 管道方式

    WebProcess->>WebProcess: 用户操作
    Note right of WebProcess: 用户在Web界面操作

    WebProcess->>Pipe: write(command)
    Note right of WebProcess: 写入控制命令到管道

    Pipe->>MainProcess: 管道数据可读
    Note right of Pipe: 管道通知主进程

    MainProcess->>InteractPlugin: 读取管道数据
    Note right of MainProcess: 主进程读取命令

    InteractPlugin->>InteractPlugin: 解析命令
    Note right of InteractPlugin: 解析控制命令

    InteractPlugin->>PluginManager: 执行控制操作
    Note right of InteractPlugin: 通过插件管理器执行

    PluginManager->>PluginManager: 处理控制命令
    Note right of PluginManager: 分发到目标插件

    PluginManager->>InteractPlugin: 返回执行结果
    InteractPlugin->>MainProcess: 返回处理结果

    MainProcess->>Pipe: write(result)
    Note right of MainProcess: 写入执行结果到管道

    Pipe->>WebProcess: 管道数据可读
    Note right of Pipe: 通知Web进程

    WebProcess->>Pipe: read(result)
    Note right of WebProcess: 读取执行结果

    WebProcess->>WebProcess: 更新界面
    Note right of WebProcess: 更新Web界面状态

    Note over WebProcess, InteractPlugin: 进程通信特点：管道双向通信，异步处理
```

# 部署视图
