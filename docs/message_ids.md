# 消息ID定义文档

这个文件仅作为文档说明，不会被程序读取。
实际的队列创建逻辑在 PluginManager 中直接实现。

## 消息ID定义

### 数据流消息（使用队列）
- `11` - DATA_SOURCE_TO_PARSER   : 数据从源到解析器
- `21` - DATA_PARSER_TO_UPLOAD   : 数据从解析器到上传器

### 控制消息（直接调用 control 接口）
- `301` - CONTROL_SOURCE          : 源插件控制消息
- `302` - CONTROL_PARSER          : 解析器插件控制消息
- `303` - CONTROL_UPLOAD          : 上传器插件控制消息

### 遥测消息（直接调用 telemetry 接口）
- `201` - TELEMETRY_SOURCE        : 源插件遥测消息
- `202` - TELEMETRY_PARSER        : 解析器插件遥测消息
- `203` - TELEMETRY_UPLOAD        : 上传器插件遥测消息

## 消息流向图

```
数据流（队列缓存，异步）：
source   -> [Queue 11]  -> parser   -> [Queue 21]  -> upload

控制流（直接同步调用）：
interactor -> control(301) -> source
interactor -> control(302) -> parser
interactor -> control(303) -> upload

遥测流（直接同步调用，monitor 主动获取）：
monitor -> telemetry() -> source   (获取遥测数据)
monitor -> telemetry() -> parser   (获取遥测数据)
monitor -> telemetry() -> upload   (获取遥测数据)
```

## 插件输入输出配置

### source 插件
- **数据输出**: [11] (队列)
- **控制输入**: [301] (直接调用 control 接口)
- **遥测输出**: [201] (monitor 直接调用 telemetry 接口获取)

### parser 插件
- **数据输入**: [11] (队列)
- **数据输出**: [21] (队列)
- **控制输入**: [302] (直接调用 control 接口)
- **遥测输出**: [202] (monitor 直接调用 telemetry 接口获取)

### upload 插件
- **数据输入**: [21] (队列)
- **控制输入**: [303] (直接调用 control 接口)
- **遥测输出**: [203] (monitor 直接调用 telemetry 接口获取)

### monitor 插件
- **遥测获取**: 主动调用其他插件的 telemetry() 接口

### interactor 插件
- **控制输出**: [301, 302, 303] (直接调用其他插件的 control 接口)
